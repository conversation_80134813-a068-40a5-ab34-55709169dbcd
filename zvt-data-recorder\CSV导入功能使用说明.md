# ZVT数据库系统 CSV导入功能使用说明

## 概述

本文档介绍了为zvt数据库系统新增的CSV文件导入功能，该功能可以将CSV格式的股票K线数据导入到数据库中。

## 功能特性

### ✅ 已实现的功能

1. **CSV文件读取和解析**
   - 支持GBK编码的中文CSV文件
   - 自动跳过说明行，正确解析数据结构
   - 智能处理股票代码格式（如：sz002436 → 002436）

2. **数据验证和清洗**
   - 验证必需字段的完整性
   - 数据类型自动转换（日期、数值等）
   - 空值和异常数据检测

3. **股票基础信息导入**
   - 自动提取股票代码和名称
   - 根据代码判断交易所（沪市/深市）
   - 生成标准化的实体ID

4. **K线数据导入**
   - 支持OHLCV基础数据（开盘价、最高价、最低价、收盘价、成交量、成交额）
   - 支持扩展字段（前收盘价、流通市值、总市值等）
   - 自动计算涨跌幅
   - 批量导入优化（每1000条提交一次）

5. **数据完整性保障**
   - 重复数据检测和更新
   - 事务管理确保数据一致性
   - 详细的导入日志和错误处理

## 导入结果

### 成功导入的数据

**文件1: sz002436.csv (兴森科技)**
- 📊 K线记录数: 3,492条
- 📅 时间范围: 2010-06-18 ~ 2025-07-03
- ✅ 数据完整性: 主要字段无空值，价格逻辑正常

**文件2: sz002435.csv (长江润发/退长康)**
- 📊 K线记录数: 3,247条  
- 📅 时间范围: 2010-06-18 ~ 2024-07-01
- ✅ 数据完整性: 主要字段无空值，价格逻辑正常

**总计导入结果:**
- 🏢 股票数量: 2只
- 📈 K线数据: 6,739条
- ✅ 成功率: 100%

## 使用方法

### 1. 快速导入（推荐）

直接运行导入脚本：

```bash
python import_csv_data.py
```

### 2. 编程方式导入

```python
from zvt_data_recorder.utils.csv_importer import import_stock_kline_csv

# 导入CSV文件
results = import_stock_kline_csv(
    csv_files=["sz002436.csv", "sz002435.csv"],
    stock_schema=Stock,
    kline_schema=StockKlineDay,
    provider="csv"
)

print(f"导入股票数: {results['total_stocks']}")
print(f"导入K线数: {results['total_klines']}")
```

### 3. 自定义导入

```python
from zvt_data_recorder.utils.csv_importer import StockKlineCSVImporter

# 创建导入器
importer = StockKlineCSVImporter(
    stock_schema=Stock,
    kline_schema=StockKlineDay,
    provider="csv"
)

# 导入单个文件
result = importer.import_from_csv(
    file_path="your_file.csv",
    import_stocks=True,
    import_klines=True
)
```

## 数据验证

运行验证脚本检查导入结果：

```bash
python verify_imported_data.py
```

验证内容包括：
- 股票基础信息完整性
- K线数据数量和时间范围
- 价格数据逻辑性检查
- 与原始CSV文件的对比验证

## CSV文件格式要求

### 支持的CSV格式

```csv
数据说明行（会被自动跳过）
股票代码,股票名称,交易日期,开盘价,最高价,最低价,收盘价,前收盘价,成交量,成交额,流通市值,总市值
sz002436,兴森科技,2010-06-18,39.0,40.77,36.6,36.93,36.5,13768340.0,528816783.3,825163920.0,**********.0
```

### 必需字段

- `股票代码`: 股票代码（支持sz/sh前缀）
- `股票名称`: 股票名称
- `交易日期`: 交易日期（YYYY-MM-DD格式）
- `开盘价`: 开盘价
- `最高价`: 最高价
- `最低价`: 最低价
- `收盘价`: 收盘价
- `成交量`: 成交量
- `成交额`: 成交额

### 可选字段

- `前收盘价`: 前一交易日收盘价
- `流通市值`: 流通市值
- `总市值`: 总市值

## 数据库存储

### 数据库文件位置
```
C:\Users\<USER>\zvt-data-recorder\data\csv\csv_stock_csv_data.db
```

### 数据表结构

**股票基础信息表 (stock)**
- `id`: 股票实体ID (如: stock_sz_002436)
- `code`: 股票代码 (如: 002436)
- `name`: 股票名称
- `exchange`: 交易所 (sh/sz)
- `timestamp`: 创建时间

**K线数据表 (stock_kline_day)**
- `id`: K线记录ID
- `entity_id`: 关联的股票实体ID
- `timestamp`: 交易日期
- `open/high/low/close`: OHLC价格数据
- `volume`: 成交量
- `turnover`: 成交额
- `pre_close`: 前收盘价
- `change_pct`: 涨跌幅
- `market_cap`: 流通市值
- `total_market_cap`: 总市值

## 查询导入的数据

```python
from zvt_data_recorder import get_db_session

# 查询股票信息
stock_session = get_db_session(provider="csv", data_schema=Stock)
stocks = stock_session.query(Stock).all()

# 查询K线数据
kline_session = get_db_session(provider="csv", data_schema=StockKlineDay)
klines = kline_session.query(StockKlineDay).filter(
    StockKlineDay.entity_id == "stock_sz_002436"
).order_by(StockKlineDay.timestamp).all()
```

## 注意事项

1. **编码格式**: CSV文件需要使用GBK编码
2. **文件格式**: 第一行为说明文字，第二行为列名，第三行开始为数据
3. **股票代码**: 支持带前缀（sz/sh）和不带前缀的格式
4. **数据更新**: 重复导入时会更新现有记录
5. **性能优化**: 大文件导入时采用批量提交策略

## 扩展功能

该CSV导入工具具有良好的扩展性，可以轻松适配其他格式的股票数据文件：

1. **支持其他编码格式**
2. **自定义字段映射**
3. **支持其他时间周期的K线数据**
4. **支持其他类型的金融数据**

## 技术实现

- **核心模块**: `src/zvt_data_recorder/utils/csv_importer.py`
- **导入脚本**: `import_csv_data.py`
- **验证脚本**: `verify_imported_data.py`
- **数据库**: SQLite (可扩展支持MySQL/PostgreSQL)
- **ORM框架**: SQLAlchemy
- **数据处理**: Pandas
