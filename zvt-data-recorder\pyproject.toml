[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "zvt-data-recorder"
version = "1.0.0"
description = "独立的数据记录库，提供完整的数据记录、存储和管理能力"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "ZVT Data Recorder Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "ZVT Data Recorder Team", email = "<EMAIL>"}
]
keywords = ["data", "recorder", "database", "finance", "quant", "time-series", "sqlalchemy", "pandas"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers", 
    "Intended Audience :: Financial and Insurance Industry",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Office/Business :: Financial :: Investment",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10", 
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
]
requires-python = ">=3.9"
dependencies = [
    "requests>=2.31.0",
    "SQLAlchemy>=2.0.0,<3.0.0",
    "pandas>=2.0.0", 
    "arrow>=1.2.0",
    "numpy>=1.24.0",
    "openpyxl>=3.1.0",
    "pytz>=2023.3",
    "pydantic>=2.0.0",
]

[project.optional-dependencies]
mysql = ["PyMySQL>=1.0.0"]
postgresql = ["psycopg2-binary>=2.9.0"]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0", 
    "mypy>=1.0.0",
]
docs = [
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
]
all = [
    "PyMySQL>=1.0.0",
    "psycopg2-binary>=2.9.0",
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0", 
    "sphinx>=6.0.0",
    "sphinx-rtd-theme>=1.2.0",
]

[project.scripts]
zvt-data-recorder = "zvt_data_recorder.cli:main"

[project.urls]
Homepage = "https://github.com/your-username/zvt-data-recorder"
Documentation = "https://zvt-data-recorder.readthedocs.io/"
Repository = "https://github.com/your-username/zvt-data-recorder.git"
"Bug Tracker" = "https://github.com/your-username/zvt-data-recorder/issues"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.json", "*.yaml", "*.yml"]

# Black 代码格式化配置
[tool.black]
line-length = 120
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort 导入排序配置
[tool.isort]
profile = "black"
line_length = 120
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# MyPy 类型检查配置
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pandas.*",
    "numpy.*", 
    "sqlalchemy.*",
    "arrow.*",
    "requests.*",
]
ignore_missing_imports = true

# Pytest 测试配置
[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

# Coverage 配置
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
