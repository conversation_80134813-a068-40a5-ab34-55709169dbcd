#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据更新模式管理模块

提供全量更新、增量更新和自动更新模式的实现
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import pandas as pd

logger = logging.getLogger(__name__)


class UpdateModeManager:
    """数据更新模式管理器"""
    
    def __init__(self, config_manager):
        """
        初始化更新模式管理器
        
        :param config_manager: 配置管理器实例
        """
        self.config = config_manager
    
    def determine_update_mode(self, level: str, entity_ids: List[str] = None) -> str:
        """
        自动确定更新模式
        
        :param level: 时间周期
        :param entity_ids: 股票ID列表
        :return: 更新模式 ('full', 'incremental')
        """
        configured_mode = self.config.get('update_mode.default_mode', 'auto')
        
        if configured_mode in ['full', 'incremental']:
            return configured_mode
        
        # 自动模式判断逻辑
        if configured_mode == 'auto':
            return self._auto_determine_mode(level, entity_ids)
        
        logger.warning(f"未知的更新模式: {configured_mode}，使用增量更新")
        return 'incremental'
    
    def _auto_determine_mode(self, level: str, entity_ids: List[str] = None) -> str:
        """
        自动判断更新模式
        
        :param level: 时间周期
        :param entity_ids: 股票ID列表
        :return: 更新模式
        """
        try:
            # 获取对应的数据模式
            from start_kline_recorder import get_schema_by_level
            schema = get_schema_by_level(level)
            
            if not schema:
                logger.warning(f"无法获取时间周期 {level} 对应的数据模式，使用全量更新")
                return 'full'
            
            # 检查数据库是否为空
            if self._is_database_empty(schema):
                logger.info(f"数据库为空，使用全量更新模式")
                return 'full'
            
            # 检查最后更新时间
            last_update_time = self._get_last_update_time(schema, entity_ids)
            if not last_update_time:
                logger.info(f"无法获取最后更新时间，使用全量更新模式")
                return 'full'
            
            # 计算距离最后更新的天数
            days_since_update = (datetime.now() - last_update_time).days
            threshold_days = self.config.get('update_mode.auto_mode_rules.full_update_threshold_days', 30)
            
            if days_since_update > threshold_days:
                logger.info(f"距离最后更新已过 {days_since_update} 天（阈值: {threshold_days}），使用全量更新模式")
                return 'full'
            
            logger.info(f"距离最后更新 {days_since_update} 天，使用增量更新模式")
            return 'incremental'
            
        except Exception as e:
            logger.error(f"自动判断更新模式失败: {e}，使用全量更新")
            return 'full'
    
    def _is_database_empty(self, schema) -> bool:
        """
        检查数据库是否为空
        
        :param schema: 数据模式类
        :return: 是否为空
        """
        try:
            from zvt_data_recorder.database.session import get_count
            count = get_count(schema, provider=self.config.get_data_provider())
            return count == 0
        except Exception as e:
            logger.error(f"检查数据库是否为空失败: {e}")
            return True
    
    def _get_last_update_time(self, schema, entity_ids: List[str] = None) -> Optional[datetime]:
        """
        获取最后更新时间
        
        :param schema: 数据模式类
        :param entity_ids: 股票ID列表
        :return: 最后更新时间
        """
        try:
            from sqlalchemy import text
            
            # 查询最新的时间戳
            df = schema.query_data(
                provider=self.config.get_data_provider(),
                entity_ids=entity_ids,
                return_type="df",
                order=text("timestamp desc"),
                limit=1
            )
            
            if df.empty:
                return None
            
            # 重置索引以确保timestamp列可访问
            if df.index.names and 'timestamp' in df.index.names:
                df = df.reset_index()
            
            if 'timestamp' not in df.columns:
                return None
            
            timestamp = df.iloc[0]['timestamp']
            if isinstance(timestamp, str):
                return pd.to_datetime(timestamp).to_pydatetime()
            elif isinstance(timestamp, pd.Timestamp):
                return timestamp.to_pydatetime()
            else:
                return timestamp
                
        except Exception as e:
            logger.error(f"获取最后更新时间失败: {e}")
            return None
    
    def get_update_time_range(self, mode: str, level: str, 
                             entity_ids: List[str] = None,
                             start_date: str = None, 
                             end_date: str = None) -> Tuple[Optional[str], Optional[str]]:
        """
        根据更新模式获取时间范围
        
        :param mode: 更新模式
        :param level: 时间周期
        :param entity_ids: 股票ID列表
        :param start_date: 指定的开始日期
        :param end_date: 指定的结束日期
        :return: (开始日期, 结束日期) 元组
        """
        # 如果明确指定了时间范围，直接使用
        if start_date and end_date:
            return start_date, end_date
        
        # 获取配置的时间范围
        config_time_range = self.config.get_time_range()
        
        # 结束日期：优先使用指定值，然后使用配置值，最后使用当前日期
        final_end_date = (end_date or 
                         config_time_range['end_date'] or 
                         datetime.now().strftime('%Y-%m-%d'))
        
        if mode == 'full':
            # 全量更新模式
            if start_date:
                final_start_date = start_date
            elif config_time_range['start_date']:
                final_start_date = config_time_range['start_date']
            else:
                # 使用默认历史天数
                history_days = self.config.get('time_range.full_update_history_days', 365)
                start_datetime = datetime.now() - timedelta(days=history_days)
                final_start_date = start_datetime.strftime('%Y-%m-%d')
            
            logger.info(f"🔄 全量更新模式 - 时间范围: {final_start_date} ~ {final_end_date}")
            return final_start_date, final_end_date
        
        elif mode == 'incremental':
            # 增量更新模式
            if start_date:
                final_start_date = start_date
            else:
                # 从最后更新时间开始，回溯几天以确保数据完整性
                from start_kline_recorder import get_schema_by_level
                schema = get_schema_by_level(level)
                
                if schema:
                    last_update_time = self._get_last_update_time(schema, entity_ids)
                    if last_update_time:
                        # 回溯配置的天数
                        lookback_days = self.config.get('time_range.incremental_lookback_days', 7)
                        start_datetime = last_update_time - timedelta(days=lookback_days)
                        final_start_date = start_datetime.strftime('%Y-%m-%d')
                    else:
                        # 如果无法获取最后更新时间，回溯30天
                        start_datetime = datetime.now() - timedelta(days=30)
                        final_start_date = start_datetime.strftime('%Y-%m-%d')
                else:
                    # 如果无法获取数据模式，回溯30天
                    start_datetime = datetime.now() - timedelta(days=30)
                    final_start_date = start_datetime.strftime('%Y-%m-%d')
            
            logger.info(f"📈 增量更新模式 - 时间范围: {final_start_date} ~ {final_end_date}")
            return final_start_date, final_end_date
        
        else:
            logger.error(f"未知的更新模式: {mode}")
            return None, None
    
    def print_update_plan(self, mode: str, levels: List[str], entity_ids: List[str],
                         start_date: str, end_date: str):
        """
        打印更新计划
        
        :param mode: 更新模式
        :param levels: 时间周期列表
        :param entity_ids: 股票ID列表
        :param start_date: 开始日期
        :param end_date: 结束日期
        """
        print(f"\n📋 数据更新计划")
        print("=" * 50)
        
        mode_names = {
            'full': '全量更新',
            'incremental': '增量更新',
            'auto': '自动模式'
        }
        
        print(f"更新模式: {mode_names.get(mode, mode)}")
        print(f"时间周期: {', '.join(levels)}")
        print(f"股票数量: {len(entity_ids) if entity_ids else '全部'}")
        print(f"时间范围: {start_date} ~ {end_date}")
        
        if entity_ids and len(entity_ids) <= 10:
            print(f"股票列表: {', '.join(entity_ids)}")
        elif entity_ids:
            print(f"股票列表: {', '.join(entity_ids[:5])}... (共{len(entity_ids)}只)")
        
        # 估算数据量
        estimated_records = self._estimate_data_volume(levels, entity_ids, start_date, end_date)
        if estimated_records:
            print(f"预估记录数: {estimated_records:,}")
    
    def _estimate_data_volume(self, levels: List[str], entity_ids: List[str],
                             start_date: str, end_date: str) -> Optional[int]:
        """
        估算数据量
        
        :param levels: 时间周期列表
        :param entity_ids: 股票ID列表
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 估算的记录数
        """
        try:
            start_dt = pd.to_datetime(start_date)
            end_dt = pd.to_datetime(end_date)
            days = (end_dt - start_dt).days + 1
            
            if not entity_ids:
                # 假设有4000只股票
                stock_count = 4000
            else:
                stock_count = len(entity_ids)
            
            # 每个时间周期的记录数估算
            level_multipliers = {
                '1d': 1,        # 每天1条
                '1h': 4,        # 每天4小时交易时间
                '30m': 8,       # 每天8个30分钟
                '15m': 16,      # 每天16个15分钟
                '5m': 48,       # 每天48个5分钟
                '1m': 240       # 每天240个1分钟
            }
            
            total_records = 0
            for level in levels:
                multiplier = level_multipliers.get(level, 1)
                # 考虑交易日（约70%的日子是交易日）
                trading_days = int(days * 0.7)
                records = stock_count * trading_days * multiplier
                total_records += records
            
            return total_records
            
        except Exception as e:
            logger.error(f"估算数据量失败: {e}")
            return None
    
    def validate_update_parameters(self, mode: str, levels: List[str], 
                                  start_date: str = None, end_date: str = None) -> bool:
        """
        验证更新参数
        
        :param mode: 更新模式
        :param levels: 时间周期列表
        :param start_date: 开始日期
        :param end_date: 结束日期
        :return: 参数是否有效
        """
        try:
            # 验证更新模式
            valid_modes = ['full', 'incremental', 'auto']
            if mode not in valid_modes:
                logger.error(f"无效的更新模式: {mode}，支持的模式: {valid_modes}")
                return False
            
            # 验证时间周期
            available_levels = self.config.get_available_levels()
            for level in levels:
                if level not in available_levels:
                    logger.error(f"无效的时间周期: {level}，支持的周期: {available_levels}")
                    return False
            
            # 验证日期格式
            if start_date:
                try:
                    pd.to_datetime(start_date)
                except:
                    logger.error(f"无效的开始日期格式: {start_date}")
                    return False
            
            if end_date:
                try:
                    pd.to_datetime(end_date)
                except:
                    logger.error(f"无效的结束日期格式: {end_date}")
                    return False
            
            # 验证日期逻辑
            if start_date and end_date:
                if pd.to_datetime(start_date) > pd.to_datetime(end_date):
                    logger.error(f"开始日期不能晚于结束日期: {start_date} > {end_date}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证更新参数失败: {e}")
            return False
