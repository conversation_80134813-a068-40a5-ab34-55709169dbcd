# -*- coding: utf-8 -*-
"""
QMT K线数据记录器使用示例
展示如何使用增强的QMT记录器进行数据采集
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from zvt_data_recorder.recorders.qmt_recorder import QmtKdataRecorder, QmtStockRecorder
from zvt_data_recorder.core.schema import Mixin
from zvt_data_recorder.database.session import get_db_session
from sqlalchemy import Column, String, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base

# 创建数据库基类
Base = declarative_base()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


class StockKlineDay(Base, Mixin):
    """日K线数据模型示例"""

    __tablename__ = 'stock_kline_day'
    
    # 继承基础字段: id, entity_id, timestamp
    code = Column(String(20), comment='股票代码')
    name = Column(String(100), comment='股票名称')
    level = Column(String(10), comment='K线级别')
    open = Column(Float, comment='开盘价')
    high = Column(Float, comment='最高价')
    low = Column(Float, comment='最低价')
    close = Column(Float, comment='收盘价')
    volume = Column(Float, comment='成交量')
    turnover = Column(Float, comment='成交额')
    provider = Column(String(32), comment='数据提供者')
    
    def _validate_business_logic(self) -> bool:
        """重写业务逻辑验证"""
        try:
            # K线数据特定验证
            if self.high and self.low and self.high < self.low:
                logger.warning(f"最高价 {self.high} 小于最低价 {self.low}")
                return False
            
            if self.open and self.high and self.low:
                if not (self.low <= self.open <= self.high):
                    logger.warning(f"开盘价 {self.open} 不在合理范围内")
                    return False
            
            if self.close and self.high and self.low:
                if not (self.low <= self.close <= self.high):
                    logger.warning(f"收盘价 {self.close} 不在合理范围内")
                    return False
            
            if self.volume and self.volume < 0:
                logger.warning(f"成交量不能为负数: {self.volume}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"K线数据业务逻辑验证失败: {e}")
            return False
    
    def _clean_business_data(self):
        """重写业务数据清洗"""
        try:
            # 确保股票代码格式正确
            if self.code:
                self.code = self.code.upper().strip()
            
            # 确保级别格式正确
            if self.level:
                self.level = self.level.lower().strip()
                
        except Exception as e:
            logger.error(f"K线数据清洗失败: {e}")


class Stock(Base, Mixin):
    """股票基础信息模型示例"""

    __tablename__ = 'stock'
    
    code = Column(String(20), comment='股票代码')
    name = Column(String(100), comment='股票名称')
    exchange = Column(String(10), comment='交易所')
    entity_type = Column(String(50), comment='实体类型')
    provider = Column(String(32), comment='数据提供者')


class EnhancedQmtKdataRecorder(QmtKdataRecorder):
    """增强的QMT K线记录器示例"""
    
    data_schema = StockKlineDay
    
    def _run_impl(self) -> List[str]:
        """实现具体的记录逻辑"""
        try:
            # 获取股票列表
            stock_list = self.get_stock_list()
            
            if not stock_list:
                self.logger.warning("未获取到股票列表")
                return []
            
            # 限制处理数量（示例）
            stock_list = stock_list[:10]  # 只处理前10只股票
            
            self.logger.info(f"准备处理 {len(stock_list)} 只股票的K线数据")
            
            # 使用增强的实体处理方法
            unfinished = self.process_entities_with_stats(
                entities=stock_list,
                process_func=self._process_single_stock,
                progress_interval=5  # 每5只股票报告一次进度
            )
            
            return unfinished
            
        except Exception as e:
            self.logger.error(f"记录器运行失败: {e}")
            raise
    
    def _process_single_stock(self, stock_code: str) -> int:
        """处理单只股票的K线数据"""
        try:
            # 设置时间范围（最近30天）
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
            
            # 获取K线数据
            records = self.record_stock_kdata(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date
            )
            
            if not records:
                self.logger.warning(f"未获取到 {stock_code} 的K线数据")
                return 0
            
            # 数据验证和清洗
            valid_records = []
            for record_data in records:
                # 创建数据对象
                record = StockKlineDay()
                
                # 填充数据
                for key, value in record_data.items():
                    if hasattr(record, key):
                        setattr(record, key, value)
                
                # 验证和清洗
                if record.validate_and_clean():
                    valid_records.append(record)
                else:
                    self.logger.warning(f"跳过无效记录: {record.id}")
            
            # 保存到数据库
            if valid_records:
                try:
                    self.session.add_all(valid_records)
                    self.session.commit()
                    self.logger.info(f"成功保存 {stock_code} 的 {len(valid_records)} 条K线数据")
                except Exception as e:
                    self.session.rollback()
                    self.logger.error(f"保存 {stock_code} K线数据失败: {e}")
                    raise
            
            return len(valid_records)
            
        except Exception as e:
            self.logger.error(f"处理股票 {stock_code} 失败: {e}")
            raise


def main():
    """主函数示例"""
    try:
        logger.info("开始QMT K线数据采集示例")
        
        # 创建记录器
        recorder = EnhancedQmtKdataRecorder(
            level='1d',  # 日K线
            adjust_type='qfq',  # 前复权
            force_update=False,
            sleeping_time=1,  # 每只股票间隔1秒
            ignore_failed=True,
            return_unfinished=True
        )
        
        # 运行记录器
        unfinished = recorder.run()
        
        # 获取统计信息
        stats = recorder.get_stats_summary()
        
        logger.info("采集完成！")
        logger.info(f"统计摘要:")
        logger.info(f"  处理实体数: {stats['processed_count']}")
        logger.info(f"  成功数: {stats['success_count']}")
        logger.info(f"  失败数: {stats['error_count']}")
        logger.info(f"  跳过数: {stats['skipped_count']}")
        logger.info(f"  总记录数: {stats['total_records']}")
        logger.info(f"  成功率: {stats['success_rate']:.1f}%")
        logger.info(f"  总耗时: {stats.get('elapsed_time', 0):.2f}秒")
        logger.info(f"  吞吐量: {stats.get('throughput', 0):.1f}条/秒")
        
        if unfinished:
            logger.warning(f"未完成的实体: {unfinished}")
        
        # 关闭记录器
        recorder.close()
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        raise


if __name__ == '__main__':
    main()
