# ZVT数据库路径配置说明

## 数据库路径的生成逻辑

数据库文件路径 `C:\Users\<USER>\zvt-data-recorder\data\csv\csv_stock_csv_data.db` 是通过以下配置和逻辑生成的：

## 1. 默认数据路径配置

### 配置文件位置
- **主配置文件**: `src/zvt_data_recorder/config/settings.py`
- **数据库引擎**: `src/zvt_data_recorder/database/engine.py`

### 默认路径生成逻辑

<augment_code_snippet path="src/zvt_data_recorder/config/settings.py" mode="EXCERPT">
````python
def _get_default_data_path(self) -> str:
    """获取默认数据路径"""
    home_dir = Path.home()  # 获取用户主目录
    data_dir = home_dir / 'zvt-data-recorder' / 'data'
    data_dir.mkdir(parents=True, exist_ok=True)
    return str(data_dir)
````
</augment_code_snippet>

**默认路径**: `{用户主目录}/zvt-data-recorder/data`
- Windows: `C:\Users\<USER>\zvt-data-recorder\data`
- Linux/Mac: `/home/<USER>/zvt-data-recorder/data`

## 2. 数据库文件路径构建

### 路径构建逻辑

<augment_code_snippet path="src/zvt_data_recorder/database/engine.py" mode="EXCERPT">
````python
# 创建提供者目录
provider_path = os.path.join(data_path, provider)
if not os.path.exists(provider_path):
    os.makedirs(provider_path)

# 构建数据库文件路径
db_file = f"{provider}_{db_name}.db"
db_path = os.path.join(provider_path, db_file)
````
</augment_code_snippet>

### 路径组成部分

1. **基础数据路径**: `{用户主目录}/zvt-data-recorder/data`
2. **提供者目录**: `csv` (在导入脚本中指定的provider)
3. **数据库文件名**: `csv_stock_csv_data.db`
   - 格式: `{provider}_{db_name}.db`
   - provider: `csv`
   - db_name: `stock_csv_data` (在导入脚本中指定)

**最终路径**: `{基础路径}/{provider}/{provider}_{db_name}.db`

## 3. 配置方式

### 方式1: 环境变量配置（推荐）

```bash
# Windows
set ZVT_DATA_PATH=D:\MyData\zvt-data

# Linux/Mac
export ZVT_DATA_PATH=/path/to/your/data
```

### 方式2: 代码中配置

```python
from zvt_data_recorder.config import init_config

# 初始化时指定数据路径
config = {
    'DATA_PATH': 'D:\\MyData\\zvt-data',  # Windows
    # 'DATA_PATH': '/path/to/your/data',  # Linux/Mac
}
init_config(config)
```

### 方式3: 修改配置文件

直接修改 `src/zvt_data_recorder/config/settings.py` 中的默认路径：

```python
def _get_default_data_path(self) -> str:
    """获取默认数据路径"""
    # 自定义路径
    return "D:\\MyData\\zvt-data"  # Windows
    # return "/path/to/your/data"  # Linux/Mac
```

## 4. 当前CSV导入的配置

在 `import_csv_data.py` 脚本中的配置：

<augment_code_snippet path="import_csv_data.py" mode="EXCERPT">
````python
register_schema(
    providers=["csv"],           # 提供者名称
    db_name="stock_csv_data",   # 数据库名称
    schema_base=Base,
    entity_type="stock"
)
````
</augment_code_snippet>

**生成的路径结构**:
```
{DATA_PATH}/
├── csv/
│   └── csv_stock_csv_data.db  # CSV股票数据
├── qmt/
│   └── qmt_stock_kline_qmt.db # QMT股票数据
└── other_providers/
    └── ...
```

## 5. 修改数据库路径的方法

### 方法1: 设置环境变量（推荐）

```bash
# Windows命令行
set ZVT_DATA_PATH=D:\MyStockData

# Windows PowerShell
$env:ZVT_DATA_PATH="D:\MyStockData"

# Linux/Mac
export ZVT_DATA_PATH="/home/<USER>/MyStockData"
```

### 方法2: 修改导入脚本

修改 `import_csv_data.py` 中的配置：

```python
def main():
    # 在初始化配置时指定数据路径
    config = {
        'DATA_PATH': 'D:\\MyStockData',  # 自定义路径
        'LOG_LEVEL': 'INFO',
    }
    init_config(config)
    # ... 其他代码
```

### 方法3: 创建配置文件

创建 `config.json` 文件：

```json
{
  "database": {
    "data_path": "D:\\MyStockData",
    "engine_type": "sqlite"
  }
}
```

然后在代码中加载：

```python
import json
from zvt_data_recorder.config import init_config

with open('config.json', 'r') as f:
    config = json.load(f)
    
init_config(config['database'])
```

## 6. 验证当前配置

运行以下代码查看当前的数据路径配置：

```python
from zvt_data_recorder.config import get_config

config = get_config()
print(f"当前数据路径: {config.DATA_PATH}")
```

或者查看日志输出，导入时会显示：
```
Created database engine for csv_stock_csv_data: {完整路径}
```

## 7. 注意事项

1. **路径权限**: 确保指定的路径有读写权限
2. **路径格式**: Windows使用反斜杠 `\` 或双反斜杠 `\\`，Linux/Mac使用正斜杠 `/`
3. **自动创建**: 系统会自动创建不存在的目录
4. **多提供者**: 不同的数据提供者会创建不同的子目录
5. **数据库名称**: 不同的db_name会生成不同的数据库文件

## 8. 常见问题

### Q: 如何迁移现有数据库到新路径？
A: 
1. 停止所有数据库操作
2. 复制整个数据目录到新位置
3. 更新配置指向新路径
4. 重新启动应用

### Q: 可以使用相对路径吗？
A: 可以，但建议使用绝对路径避免路径混乱

### Q: 如何备份数据库？
A: 直接复制 `.db` 文件即可，SQLite是单文件数据库

### Q: 支持网络路径吗？
A: SQLite支持网络路径，但性能可能受影响，建议使用本地路径
