# 变更日志

本文档记录了ZVT Data Recorder项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [1.0.0] - 2024-01-20

### 新增
- 🎉 首次发布ZVT Data Recorder独立库
- 📊 核心数据记录器框架
  - `Recorder`: 数据记录器基类
  - `EntityEventRecorder`: 实体事件记录器
  - `TimeSeriesDataRecorder`: 时间序列数据记录器
  - `FixedCycleDataRecorder`: 固定周期数据记录器
- 💾 数据模型基础类
  - `Mixin`: 所有数据模型的基类
  - `TradableEntity`: 可交易实体基类
- 🗄️ 数据库管理系统
  - 多数据库支持 (SQLite, MySQL, PostgreSQL)
  - 自动引擎和会话管理
  - 灵活的模式注册机制
- 🔧 状态管理服务
  - `OneStateService`: 单状态服务
  - `EntityStateService`: 实体状态服务
  - 状态持久化和恢复
- 🛠️ 实用工具集
  - 时间处理工具 (`time_utils`)
  - 数据处理工具 (`data_utils`)
  - 字符串处理工具 (`str_utils`)
  - 记录器工具 (`recorder_utils`)
- 📧 通知系统
  - 邮件通知支持
  - 错误和成功通知模板
  - 数据报告邮件
- ⚙️ 配置管理
  - 环境变量配置支持
  - 灵活的配置系统
  - 日志配置管理
- 🎯 枚举类型定义
  - `IntervalLevel`: 时间间隔级别
  - `AdjustType`: 复权类型
  - `TradableType`: 可交易类型
  - `Exchange`: 交易所枚举
- 📝 命令行工具
  - 系统信息查看
  - 记录器管理
  - 数据库连接测试
  - 示例运行
- 📚 完整文档和示例
  - API文档
  - 基础使用示例
  - 高级用法示例
  - 详细的README

### 技术特性
- ✅ Python 3.9+ 支持
- ✅ 基于SQLAlchemy 2.0+
- ✅ 支持pandas数据处理
- ✅ 异步和同步操作支持
- ✅ 完整的类型提示
- ✅ 单元测试覆盖
- ✅ 代码质量检查 (black, flake8, mypy)

### 安装和部署
- 📦 PyPI包发布支持
- 🐳 Docker支持 (计划中)
- 📖 详细的安装文档

### 从ZVT项目提取的功能
- 🔄 完整的数据记录框架
- 📈 时间序列数据处理
- 🏢 实体管理系统
- 💾 数据持久化机制
- 🔧 状态管理功能
- 📊 数据验证和清洗
- 🚨 错误处理和重试机制

## [计划中的功能]

### [1.1.0] - 计划中
- 🐳 Docker容器支持
- 🔄 更多数据源连接器
- 📊 数据质量监控
- 🎯 性能优化
- 📈 更多技术指标计算
- 🔍 数据查询优化

### [1.2.0] - 计划中
- 🌐 Web管理界面
- 📊 数据可视化
- 🔄 实时数据流支持
- 📱 移动端支持
- 🤖 智能数据修复

### [2.0.0] - 长期计划
- 🚀 分布式数据记录
- ☁️ 云原生支持
- 🤖 机器学习集成
- 📊 高级分析功能

## 贡献指南

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- 感谢 [ZVT项目](https://github.com/zvtvz/zvt) 提供的原始数据记录功能
- 感谢所有贡献者和用户的支持
