# -*- coding: utf-8 -*-
"""
数据库引擎管理

从ZVT项目中提取的数据库引擎管理功能。
"""

import json
import logging
import os
import platform
from typing import Type

from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.ext.declarative import DeclarativeMeta

from ..core.context import get_context
from ..config.settings import get_config

logger = logging.getLogger(__name__)


def _get_db_name(data_schema: DeclarativeMeta) -> str:
    """
    获取数据模式的数据库名称

    :param data_schema: 数据模式
    :return: 数据库名称
    """
    context = get_context()
    for db_name, bases in context.dbname_map_base.items():
        # bases可能是列表，需要检查每个base
        if isinstance(bases, list):
            for base in bases:
                try:
                    if isinstance(base, type) and issubclass(data_schema, base):
                        return db_name
                except TypeError:
                    continue
        else:
            # 单个base的情况
            try:
                if isinstance(bases, type) and issubclass(data_schema, bases):
                    return db_name
            except TypeError:
                continue

    # 如果没有找到，使用类名作为数据库名
    return data_schema.__name__.lower()


def get_db_engine(
    provider: str, 
    db_name: str = None, 
    data_schema: Type = None, 
    data_path: str = None
) -> Engine:
    """
    获取数据库引擎
    
    :param provider: 数据提供者
    :param db_name: 数据库名称
    :param data_schema: 数据模式
    :param data_path: 数据路径
    :return: 数据库引擎
    """
    if data_schema:
        db_name = _get_db_name(data_schema=data_schema)
    
    if not db_name:
        raise ValueError("db_name or data_schema must be provided")
    
    if not data_path:
        config = get_config()
        data_path = config.DATA_PATH
    
    # 创建提供者目录
    provider_path = os.path.join(data_path, provider)
    if not os.path.exists(provider_path):
        os.makedirs(provider_path)
    
    # 构建数据库文件路径
    db_file = f"{provider}_{db_name}.db"
    db_path = os.path.join(provider_path, db_file)
    
    # 检查缓存
    context = get_context()
    engine_key = f"{provider}_{db_name}"
    db_engine = context.db_engine_map.get(engine_key)
    
    if not db_engine:
        # 创建数据库引擎
        db_url = f"sqlite:///{db_path}?check_same_thread=False"
        
        db_engine = create_engine(
            db_url,
            echo=False,
            json_serializer=lambda obj: json.dumps(obj, ensure_ascii=False),
            pool_pre_ping=True,  # 连接池预检查
            pool_recycle=3600,   # 连接回收时间
        )
        
        # 缓存引擎
        context.db_engine_map[engine_key] = db_engine
        
        logger.info(f"Created database engine for {provider}_{db_name}: {db_path}")
    
    return db_engine


def create_db_engine(
    db_url: str,
    echo: bool = False,
    pool_size: int = 5,
    max_overflow: int = 10,
    pool_timeout: int = 30,
    pool_recycle: int = 3600,
    **kwargs
) -> Engine:
    """
    创建自定义数据库引擎
    
    :param db_url: 数据库URL
    :param echo: 是否打印SQL语句
    :param pool_size: 连接池大小
    :param max_overflow: 最大溢出连接数
    :param pool_timeout: 连接超时时间
    :param pool_recycle: 连接回收时间
    :param kwargs: 其他参数
    :return: 数据库引擎
    """
    engine_kwargs = {
        'echo': echo,
        'json_serializer': lambda obj: json.dumps(obj, ensure_ascii=False),
        'pool_pre_ping': True,
        **kwargs
    }
    
    # SQLite不支持连接池参数
    if not db_url.startswith('sqlite'):
        engine_kwargs.update({
            'pool_size': pool_size,
            'max_overflow': max_overflow,
            'pool_timeout': pool_timeout,
            'pool_recycle': pool_recycle,
        })
    
    return create_engine(db_url, **engine_kwargs)


def get_supported_databases():
    """
    获取支持的数据库类型
    
    :return: 支持的数据库类型列表
    """
    supported = ['sqlite']
    
    try:
        import pymysql
        supported.append('mysql')
    except ImportError:
        pass
    
    try:
        import psycopg2
        supported.append('postgresql')
    except ImportError:
        pass
    
    return supported


def test_db_connection(engine: Engine) -> bool:
    """
    测试数据库连接
    
    :param engine: 数据库引擎
    :return: 连接是否成功
    """
    try:
        with engine.connect() as conn:
            conn.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False


def get_db_info(engine: Engine) -> dict:
    """
    获取数据库信息
    
    :param engine: 数据库引擎
    :return: 数据库信息字典
    """
    info = {
        'url': str(engine.url),
        'driver': engine.driver,
        'dialect': engine.dialect.name,
    }
    
    try:
        with engine.connect() as conn:
            # 获取数据库版本
            if engine.dialect.name == 'sqlite':
                result = conn.execute("SELECT sqlite_version()")
                info['version'] = result.fetchone()[0]
            elif engine.dialect.name == 'mysql':
                result = conn.execute("SELECT VERSION()")
                info['version'] = result.fetchone()[0]
            elif engine.dialect.name == 'postgresql':
                result = conn.execute("SELECT version()")
                info['version'] = result.fetchone()[0]
    except Exception as e:
        logger.warning(f"Failed to get database version: {e}")
        info['version'] = 'unknown'
    
    return info


def cleanup_engines():
    """清理所有数据库引擎连接"""
    context = get_context()
    
    for engine_key, engine in context.db_engine_map.items():
        try:
            engine.dispose()
            logger.info(f"Disposed database engine: {engine_key}")
        except Exception as e:
            logger.error(f"Failed to dispose engine {engine_key}: {e}")
    
    context.db_engine_map.clear()


def get_engine_stats():
    """
    获取引擎统计信息
    
    :return: 引擎统计信息
    """
    context = get_context()
    stats = {}
    
    for engine_key, engine in context.db_engine_map.items():
        try:
            pool = engine.pool
            stats[engine_key] = {
                'size': pool.size(),
                'checked_in': pool.checkedin(),
                'checked_out': pool.checkedout(),
                'overflow': pool.overflow(),
                'invalid': pool.invalid(),
            }
        except Exception as e:
            stats[engine_key] = {'error': str(e)}
    
    return stats


__all__ = [
    "get_db_engine",
    "create_db_engine", 
    "get_supported_databases",
    "test_db_connection",
    "get_db_info",
    "cleanup_engines",
    "get_engine_stats",
]
