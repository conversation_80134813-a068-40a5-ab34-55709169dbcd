# -*- coding: utf-8 -*-
"""
字符串处理工具

从ZVT项目中提取的字符串处理相关工具函数。
"""

import re


def to_snake_str(camel_str):
    """
    将驼峰命名转换为下划线命名
    
    :param camel_str: 驼峰命名字符串
    :return: 下划线命名字符串
    """
    if not camel_str:
        return camel_str
    
    # 在大写字母前插入下划线
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', camel_str)
    # 处理连续大写字母
    s2 = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1)
    
    return s2.lower()


def to_camel_str(snake_str, first_upper=False):
    """
    将下划线命名转换为驼峰命名
    
    :param snake_str: 下划线命名字符串
    :param first_upper: 首字母是否大写
    :return: 驼峰命名字符串
    """
    if not snake_str:
        return snake_str
    
    components = snake_str.split('_')
    if first_upper:
        return ''.join(word.capitalize() for word in components)
    else:
        return components[0] + ''.join(word.capitalize() for word in components[1:])


def normalize_code(code):
    """
    标准化代码格式
    
    :param code: 原始代码
    :return: 标准化后的代码
    """
    if not code:
        return code
    
    # 移除空格和特殊字符
    code = str(code).strip().upper()
    
    # 处理股票代码格式
    if len(code) == 6 and code.isdigit():
        # 根据代码判断交易所
        if code.startswith(('60', '68', '11', '12', '13')):
            return f"sh.{code}"
        elif code.startswith(('00', '30', '12', '13')):
            return f"sz.{code}"
        elif code.startswith(('8', '4')):
            return f"bj.{code}"
    
    return code


def extract_numbers(text):
    """
    从文本中提取数字
    
    :param text: 输入文本
    :return: 数字列表
    """
    if not text:
        return []
    
    # 匹配整数和小数
    numbers = re.findall(r'-?\d+\.?\d*', str(text))
    return [float(num) if '.' in num else int(num) for num in numbers]


def clean_text(text):
    """
    清理文本，移除多余的空格和特殊字符
    
    :param text: 输入文本
    :return: 清理后的文本
    """
    if not text:
        return text
    
    # 移除多余的空格
    text = re.sub(r'\s+', ' ', str(text).strip())
    
    # 移除特殊字符（保留中文、英文、数字、基本标点）
    text = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()（）【】""''、。，！？；：]', '', text)
    
    return text


def is_chinese(text):
    """
    判断文本是否包含中文
    
    :param text: 输入文本
    :return: 是否包含中文
    """
    if not text:
        return False
    
    return bool(re.search(r'[\u4e00-\u9fff]', str(text)))


def extract_chinese(text):
    """
    提取文本中的中文字符
    
    :param text: 输入文本
    :return: 中文字符串
    """
    if not text:
        return ""
    
    chinese_chars = re.findall(r'[\u4e00-\u9fff]+', str(text))
    return ''.join(chinese_chars)


def extract_english(text):
    """
    提取文本中的英文字符
    
    :param text: 输入文本
    :return: 英文字符串
    """
    if not text:
        return ""
    
    english_chars = re.findall(r'[a-zA-Z]+', str(text))
    return ''.join(english_chars)


def format_number(number, precision=2, unit=''):
    """
    格式化数字显示
    
    :param number: 数字
    :param precision: 精度
    :param unit: 单位
    :return: 格式化后的字符串
    """
    if number is None:
        return "N/A"
    
    try:
        num = float(number)
        
        # 处理大数字
        if abs(num) >= 100000000:  # 亿
            return f"{num/100000000:.{precision}f}亿{unit}"
        elif abs(num) >= 10000:  # 万
            return f"{num/10000:.{precision}f}万{unit}"
        else:
            return f"{num:.{precision}f}{unit}"
    except:
        return str(number)


def format_percentage(value, precision=2):
    """
    格式化百分比显示
    
    :param value: 数值（0.1 表示 10%）
    :param precision: 精度
    :return: 百分比字符串
    """
    if value is None:
        return "N/A"
    
    try:
        return f"{float(value) * 100:.{precision}f}%"
    except:
        return str(value)


def truncate_text(text, max_length=50, suffix="..."):
    """
    截断文本
    
    :param text: 输入文本
    :param max_length: 最大长度
    :param suffix: 后缀
    :return: 截断后的文本
    """
    if not text:
        return text
    
    text = str(text)
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def validate_email(email):
    """
    验证邮箱格式
    
    :param email: 邮箱地址
    :return: 是否有效
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, str(email)))


def generate_id(*args):
    """
    生成ID字符串
    
    :param args: 参数列表
    :return: ID字符串
    """
    parts = []
    for arg in args:
        if arg is not None:
            parts.append(str(arg))
    
    return "_".join(parts)


__all__ = [
    # 命名转换
    "to_snake_str",
    "to_camel_str",
    
    # 代码处理
    "normalize_code",
    
    # 文本处理
    "extract_numbers",
    "clean_text",
    "is_chinese",
    "extract_chinese", 
    "extract_english",
    "truncate_text",
    
    # 格式化
    "format_number",
    "format_percentage",
    
    # 验证
    "validate_email",
    
    # ID生成
    "generate_id",
]
