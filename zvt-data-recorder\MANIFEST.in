# 包含额外的文件到分发包中

# 包含文档文件
include README.md
include LICENSE
include CHANGELOG.md
include MANIFEST.in

# 包含配置文件
include pyproject.toml
include setup.py
include requirements.txt

# 包含源代码中的数据文件
recursive-include src/zvt_data_recorder *.py
recursive-include src/zvt_data_recorder *.json
recursive-include src/zvt_data_recorder *.yaml
recursive-include src/zvt_data_recorder *.yml
recursive-include src/zvt_data_recorder *.txt

# 包含测试文件
recursive-include tests *.py

# 包含文档
recursive-include docs *.rst
recursive-include docs *.md
recursive-include docs *.txt
recursive-include docs Makefile
recursive-include docs *.bat

# 包含示例
recursive-include examples *.py
recursive-include examples *.md
recursive-include examples *.txt
recursive-include examples *.json

# 排除不需要的文件
global-exclude *.pyc
global-exclude *.pyo
global-exclude *.pyd
global-exclude __pycache__
global-exclude .git*
global-exclude .DS_Store
global-exclude *.so
global-exclude *.egg-info
