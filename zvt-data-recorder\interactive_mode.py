#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
交互式运行模式模块

提供用户友好的命令行交互界面
"""

import logging
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class InteractiveMode:
    """交互式模式管理器"""
    
    def __init__(self, config_manager, update_manager):
        """
        初始化交互式模式管理器
        
        :param config_manager: 配置管理器
        :param update_manager: 更新模式管理器
        """
        self.config = config_manager
        self.update_manager = update_manager
    
    def run_interactive_mode(self) -> Dict[str, Any]:
        """
        运行交互式模式
        
        :return: 用户选择的配置字典
        """
        print("\n🎯 欢迎使用ZVT K线数据记录器交互式模式")
        print("=" * 60)
        print("请根据提示选择您的配置选项")
        
        try:
            # 1. 选择更新模式
            update_mode = self._select_update_mode()
            
            # 2. 选择时间周期
            levels = self._select_time_levels()
            
            # 3. 选择股票范围
            entity_ids = self._select_stocks()
            
            # 4. 配置时间范围
            start_date, end_date = self._configure_time_range(update_mode, levels, entity_ids)
            
            # 5. 配置其他选项
            other_options = self._configure_other_options()
            
            # 6. 显示配置摘要并确认
            config = {
                'update_mode': update_mode,
                'levels': levels,
                'entity_ids': entity_ids,
                'start_date': start_date,
                'end_date': end_date,
                **other_options
            }
            
            if self._confirm_configuration(config):
                return config
            else:
                print("\n❌ 用户取消操作")
                return None
                
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断操作")
            return None
        except Exception as e:
            logger.error(f"交互式模式运行失败: {e}")
            print(f"\n❌ 交互式模式运行失败: {e}")
            return None
    
    def _select_update_mode(self) -> str:
        """选择更新模式"""
        print("\n📋 1. 选择数据更新模式")
        print("-" * 30)
        
        modes = {
            '1': ('auto', '自动模式 (推荐) - 智能判断使用全量或增量更新'),
            '2': ('full', '全量更新 - 重新获取指定时间范围内的所有数据'),
            '3': ('incremental', '增量更新 - 只获取最新的数据')
        }
        
        for key, (mode, desc) in modes.items():
            print(f"  {key}. {desc}")
        
        while True:
            choice = input(f"\n请选择更新模式 [1-3] (默认: 1): ").strip()
            if not choice:
                choice = '1'

            if choice in modes:
                selected_mode, desc = modes[choice]
                print(f"✅ 已选择: {desc}")

                # 二次确认更新模式
                if self.config.get('interactive.defaults.require_update_mode_confirmation', True):
                    return self._confirm_update_mode(selected_mode, desc)

                return selected_mode
            else:
                print("❌ 无效选择，请重新输入")

    def _confirm_update_mode(self, selected_mode: str, desc: str) -> str:
        """二次确认更新模式"""
        print(f"\n⚠️  更新模式确认")
        print("=" * 40)
        print(f"您选择的更新模式: {desc}")

        # 显示更新模式的详细说明和影响
        if selected_mode == "full":
            print("\n📋 全量更新模式说明:")
            print("• 将重新获取指定时间范围内的所有数据")
            print("• 会覆盖数据库中已有的数据")
            print("• 适用于数据修复或首次导入")
            print("• ⚠️  注意：此操作可能耗时较长")
        elif selected_mode == "incremental":
            print("\n📋 增量更新模式说明:")
            print("• 只获取最新的数据（从最后记录时间开始）")
            print("• 不会影响已有的历史数据")
            print("• 适用于日常数据更新")
            print("• ✅ 推荐用于定期数据维护")
        elif selected_mode == "auto":
            print("\n📋 自动模式说明:")
            print("• 系统将智能判断使用全量或增量更新")
            print("• 数据库为空或数据过旧时使用全量更新")
            print("• 数据较新时使用增量更新")
            print("• ✅ 推荐模式，适合大多数情况")

        while True:
            confirm = input(f"\n确认使用 '{desc}' 吗？[y/N]: ").strip().lower()
            if confirm in ['y', 'yes', '是', '确认']:
                print(f"✅ 确认使用: {desc}")
                return selected_mode
            elif confirm in ['n', 'no', '否', '取消', '']:
                print("❌ 已取消，请重新选择更新模式")
                return self._select_update_mode()  # 重新选择
            else:
                print("❌ 请输入 y(是) 或 n(否)")

    def _select_time_levels(self) -> List[str]:
        """选择时间周期"""
        print("\n📊 2. 选择时间周期")
        print("-" * 30)
        
        available_levels = self.config.get_available_levels()
        level_descriptions = {
            '1d': '日K线 (推荐)',
            '1h': '1小时K线',
            '30m': '30分钟K线',
            '15m': '15分钟K线',
            '5m': '5分钟K线',
            '1m': '1分钟K线'
        }
        
        print("可选的时间周期:")
        for i, level in enumerate(available_levels, 1):
            desc = level_descriptions.get(level, level)
            print(f"  {i}. {desc}")
        
        print("\n选择方式:")
        print("  - 输入数字选择单个周期，如: 1")
        print("  - 输入多个数字选择多个周期，如: 1,2,3")
        print("  - 输入 'all' 选择所有周期")
        
        default_levels = self.config.get_default_levels()
        default_indices = [str(available_levels.index(level) + 1) for level in default_levels if level in available_levels]
        default_display = ','.join(default_indices)
        
        while True:
            choice = input(f"\n请选择时间周期 (默认: {default_display}): ").strip()
            if not choice:
                choice = default_display
            
            try:
                if choice.lower() == 'all':
                    selected_levels = available_levels.copy()
                else:
                    indices = [int(x.strip()) for x in choice.split(',')]
                    selected_levels = []
                    for idx in indices:
                        if 1 <= idx <= len(available_levels):
                            selected_levels.append(available_levels[idx - 1])
                        else:
                            raise ValueError(f"无效的选择: {idx}")
                
                if selected_levels:
                    print(f"✅ 已选择: {', '.join(selected_levels)}")
                    return selected_levels
                else:
                    print("❌ 请至少选择一个时间周期")
                    
            except ValueError as e:
                print(f"❌ 输入格式错误: {e}")
    
    def _select_stocks(self) -> Optional[List[str]]:
        """选择股票范围"""
        print("\n🏢 3. 选择股票范围")
        print("-" * 30)
        
        print("选择方式:")
        print("  1. 全部股票 (推荐)")
        print("  2. 指定股票代码")
        print("  3. 使用配置文件中的默认股票")
        
        while True:
            choice = input("\n请选择股票范围 [1-3] (默认: 1): ").strip()
            if not choice:
                choice = '1'
            
            if choice == '1':
                print("✅ 已选择: 全部股票")
                return None
            elif choice == '2':
                return self._input_stock_codes()
            elif choice == '3':
                default_codes = self.config.get_default_codes()
                if default_codes:
                    print(f"✅ 已选择配置文件中的默认股票: {', '.join(default_codes[:10])}{'...' if len(default_codes) > 10 else ''}")
                    return default_codes
                else:
                    print("⚠️ 配置文件中没有设置默认股票，将使用全部股票")
                    return None
            else:
                print("❌ 无效选择，请重新输入")
    
    def _input_stock_codes(self) -> List[str]:
        """输入股票代码"""
        print("\n请输入股票代码:")
        print("  - 多个代码用逗号分隔，如: 600000,000001,002600")
        print("  - 支持6位数字代码，如: 600000")
        print("  - 输入 'cancel' 返回上级菜单")
        
        while True:
            codes_input = input("\n股票代码: ").strip()
            
            if codes_input.lower() == 'cancel':
                return self._select_stocks()
            
            if not codes_input:
                print("❌ 请输入股票代码")
                continue
            
            try:
                codes = [code.strip() for code in codes_input.split(',')]
                valid_codes = []
                
                for code in codes:
                    # 验证股票代码格式
                    if len(code) == 6 and code.isdigit():
                        valid_codes.append(code)
                    else:
                        print(f"⚠️ 无效的股票代码格式: {code} (应为6位数字)")
                
                if valid_codes:
                    print(f"✅ 已选择 {len(valid_codes)} 只股票: {', '.join(valid_codes[:10])}{'...' if len(valid_codes) > 10 else ''}")
                    return valid_codes
                else:
                    print("❌ 没有有效的股票代码")
                    
            except Exception as e:
                print(f"❌ 输入格式错误: {e}")
    
    def _configure_time_range(self, update_mode: str, levels: List[str], 
                             entity_ids: Optional[List[str]]) -> Tuple[Optional[str], Optional[str]]:
        """配置时间范围"""
        print("\n📅 4. 配置时间范围")
        print("-" * 30)
        
        # 根据更新模式自动确定时间范围
        if update_mode == 'auto':
            # 对于自动模式，需要先确定实际的更新模式
            actual_mode = self.update_manager.determine_update_mode(levels[0], entity_ids)
            print(f"自动模式判断结果: {actual_mode}")
            start_date, end_date = self.update_manager.get_update_time_range(
                actual_mode, levels[0], entity_ids
            )
        else:
            start_date, end_date = self.update_manager.get_update_time_range(
                update_mode, levels[0], entity_ids
            )
        
        print(f"建议的时间范围: {start_date} ~ {end_date}")
        
        print("\n选择方式:")
        print("  1. 使用建议的时间范围 (推荐)")
        print("  2. 自定义时间范围")
        
        while True:
            choice = input("\n请选择 [1-2] (默认: 1): ").strip()
            if not choice:
                choice = '1'
            
            if choice == '1':
                print(f"✅ 已选择建议的时间范围: {start_date} ~ {end_date}")
                return start_date, end_date
            elif choice == '2':
                return self._input_custom_time_range()
            else:
                print("❌ 无效选择，请重新输入")
    
    def _input_custom_time_range(self) -> Tuple[Optional[str], Optional[str]]:
        """输入自定义时间范围"""
        print("\n请输入时间范围 (格式: YYYY-MM-DD):")
        
        # 输入开始日期
        while True:
            start_input = input("开始日期 (留空表示自动): ").strip()
            if not start_input:
                start_date = None
                break
            
            try:
                import pandas as pd
                pd.to_datetime(start_input)
                start_date = start_input
                break
            except:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        
        # 输入结束日期
        while True:
            end_input = input("结束日期 (留空表示今天): ").strip()
            if not end_input:
                from datetime import datetime
                end_date = datetime.now().strftime('%Y-%m-%d')
                break
            
            try:
                import pandas as pd
                pd.to_datetime(end_input)
                end_date = end_input
                break
            except:
                print("❌ 日期格式错误，请使用 YYYY-MM-DD 格式")
        
        print(f"✅ 已设置时间范围: {start_date or '自动'} ~ {end_date}")
        return start_date, end_date
    
    def _configure_other_options(self) -> Dict[str, Any]:
        """配置其他选项"""
        print("\n⚙️ 5. 其他选项配置")
        print("-" * 30)
        
        options = {}
        
        # 强制更新选项
        force_update = self._ask_yes_no(
            "是否强制更新已有数据？",
            default=self.config.get_force_update()
        )
        options['force_update'] = force_update
        
        # 休眠时间配置
        default_sleep = self.config.get_sleeping_time()
        while True:
            sleep_input = input(f"记录间隔休眠时间（秒）[默认: {default_sleep}]: ").strip()
            if not sleep_input:
                options['sleeping_time'] = default_sleep
                break
            
            try:
                sleep_time = int(sleep_input)
                if sleep_time >= 0:
                    options['sleeping_time'] = sleep_time
                    break
                else:
                    print("❌ 休眠时间不能为负数")
            except ValueError:
                print("❌ 请输入有效的数字")
        
        # 详细日志选项
        verbose = self._ask_yes_no(
            "是否启用详细日志？",
            default=self.config.is_verbose()
        )
        options['verbose'] = verbose
        
        return options
    
    def _ask_yes_no(self, question: str, default: bool = False) -> bool:
        """询问是/否问题"""
        default_text = "Y/n" if default else "y/N"
        while True:
            answer = input(f"{question} [{default_text}]: ").strip().lower()
            if not answer:
                return default
            elif answer in ['y', 'yes', '是', '1']:
                return True
            elif answer in ['n', 'no', '否', '0']:
                return False
            else:
                print("❌ 请输入 y/yes/是 或 n/no/否")
    
    def _confirm_configuration(self, config: Dict[str, Any]) -> bool:
        """确认配置"""
        print("\n📋 6. 配置确认")
        print("=" * 50)
        
        # 显示配置摘要
        print("您的配置如下:")
        print(f"  更新模式: {config['update_mode']}")
        print(f"  时间周期: {', '.join(config['levels'])}")
        
        if config['entity_ids']:
            if len(config['entity_ids']) <= 10:
                print(f"  股票代码: {', '.join(config['entity_ids'])}")
            else:
                print(f"  股票代码: {', '.join(config['entity_ids'][:5])}... (共{len(config['entity_ids'])}只)")
        else:
            print("  股票范围: 全部股票")
        
        print(f"  时间范围: {config['start_date'] or '自动'} ~ {config['end_date']}")
        print(f"  强制更新: {'是' if config['force_update'] else '否'}")
        print(f"  休眠时间: {config['sleeping_time']}秒")
        print(f"  详细日志: {'是' if config['verbose'] else '否'}")
        
        # 显示更新计划
        if hasattr(self.update_manager, 'print_update_plan'):
            self.update_manager.print_update_plan(
                config['update_mode'],
                config['levels'],
                config['entity_ids'],
                config['start_date'],
                config['end_date']
            )
        
        print("\n" + "=" * 50)
        return self._ask_yes_no("确认开始执行？", default=True)
