#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
K线记录器配置管理模块

提供配置文件加载、验证和管理功能
"""

import os
import yaml
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


class KlineConfigManager:
    """K线记录器配置管理器"""
    
    def __init__(self, config_file: str = None):
        """
        初始化配置管理器
        
        :param config_file: 配置文件路径，默认为 kline_config.yaml
        """
        self.config_file = config_file or os.path.join(
            os.path.dirname(__file__), 'kline_config.yaml'
        )
        self.config = {}
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                        self.config = yaml.safe_load(f) or {}
                    elif self.config_file.endswith('.json'):
                        self.config = json.load(f)
                    else:
                        raise ValueError(f"不支持的配置文件格式: {self.config_file}")
                
                logger.info(f"✅ 成功加载配置文件: {self.config_file}")
            else:
                logger.warning(f"⚠️ 配置文件不存在: {self.config_file}，使用默认配置")
                self._create_default_config()
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self.config = {
            'basic': {
                'data_path': 'D:\\MyData\\zvt-data',
                'data_provider': 'qmt',
                'database': {
                    'db_name': 'stock_kline_qmt',
                    'engine_type': 'sqlite',
                    'echo': False
                }
            },
            'time_levels': {
                'available_levels': ['1d', '1h', '30m', '15m', '5m', '1m'],
                'default_levels': ['1d'],
                'priority_order': ['1d', '1h', '30m', '15m', '5m', '1m']
            },
            'time_range': {
                'default_start_date': None,
                'default_end_date': None,
                'incremental_lookback_days': 7,
                'full_update_history_days': 365
            },
            'recorder': {
                'default_sleeping_time': 1,
                'batch_size': 100,
                'default_retry_times': 3,
                'force_update': False
            },
            'update_mode': {
                'default_mode': 'auto',
                'auto_mode_rules': {
                    'empty_db_use_full': True,
                    'full_update_threshold_days': 30,
                    'max_incremental_days': 7
                }
            },
            'logging': {
                'level': 'INFO',
                'verbose': False
            },
            'query': {
                'default_level': '1d',
                'default_limit': 10
            },
            'interactive': {
                'enabled': False
            }
        }
    
    def get(self, key_path: str, default=None):
        """
        获取配置值
        
        :param key_path: 配置键路径，如 'basic.data_path'
        :param default: 默认值
        :return: 配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        :param key_path: 配置键路径
        :param value: 配置值
        """
        keys = key_path.split('.')
        config = self.config
        
        # 创建嵌套字典结构
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def update_from_args(self, args):
        """
        从命令行参数更新配置
        
        :param args: argparse.Namespace 对象
        """
        # 映射命令行参数到配置路径
        arg_mapping = {
            'data_path': 'basic.data_path',
            'verbose': 'logging.verbose',
            'sleeping_time': 'recorder.default_sleeping_time',
            'force_update': 'recorder.force_update',
            'level': 'query.default_level',
            'limit': 'query.default_limit'
        }
        
        for arg_name, config_path in arg_mapping.items():
            if hasattr(args, arg_name) and getattr(args, arg_name) is not None:
                self.set(config_path, getattr(args, arg_name))
        
        # 特殊处理
        if hasattr(args, 'levels') and args.levels:
            self.set('time_levels.default_levels', args.levels)
        
        if hasattr(args, 'codes') and args.codes:
            self.set('stock_filter.default_codes', args.codes)
        
        if hasattr(args, 'start_date') and args.start_date:
            self.set('time_range.default_start_date', args.start_date)
        
        if hasattr(args, 'end_date') and args.end_date:
            self.set('time_range.default_end_date', args.end_date)
    
    def get_available_levels(self) -> List[str]:
        """获取可用的时间周期列表"""
        return self.get('time_levels.available_levels', ['1d', '1h', '30m', '15m', '5m', '1m'])
    
    def get_default_levels(self) -> List[str]:
        """获取默认的时间周期列表"""
        return self.get('time_levels.default_levels', ['1d'])
    
    def get_data_path(self) -> str:
        """获取数据存储路径"""
        return self.get('basic.data_path', 'D:\\MyData\\zvt-data')
    
    def get_data_provider(self) -> str:
        """获取数据提供者"""
        return self.get('basic.data_provider', 'qmt')
    
    def get_db_name(self) -> str:
        """获取数据库名称"""
        return self.get('basic.database.db_name', 'stock_kline_qmt')
    
    def get_sleeping_time(self) -> int:
        """获取休眠时间"""
        return self.get('recorder.default_sleeping_time', 1)
    
    def get_force_update(self) -> bool:
        """获取是否强制更新"""
        return self.get('recorder.force_update', False)
    
    def get_default_codes(self) -> List[str]:
        """获取默认股票代码列表"""
        return self.get('stock_filter.default_codes', [])
    
    def get_time_range(self) -> Dict[str, Optional[str]]:
        """获取时间范围配置"""
        start_date = self.get('time_range.default_start_date')
        end_date = self.get('time_range.default_end_date')
        
        # 如果没有配置结束日期，使用当前日期
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        return {
            'start_date': start_date,
            'end_date': end_date
        }
    
    def get_update_mode(self) -> str:
        """获取更新模式"""
        return self.get('update_mode.default_mode', 'auto')
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return self.get('logging.level', 'INFO')
    
    def is_verbose(self) -> bool:
        """是否启用详细日志"""
        return self.get('logging.verbose', False)
    
    def is_interactive_enabled(self) -> bool:
        """是否启用交互式模式"""
        return self.get('interactive.enabled', False)
    
    def get_query_defaults(self) -> Dict[str, Any]:
        """获取查询默认配置"""
        return {
            'level': self.get('query.default_level', '1d'),
            'limit': self.get('query.default_limit', 10)
        }
    
    def save_config(self, file_path: str = None):
        """
        保存配置到文件
        
        :param file_path: 保存路径，默认为原配置文件路径
        """
        save_path = file_path or self.config_file
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                if save_path.endswith('.yaml') or save_path.endswith('.yml'):
                    yaml.dump(self.config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif save_path.endswith('.json'):
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                else:
                    raise ValueError(f"不支持的文件格式: {save_path}")
            
            logger.info(f"✅ 配置已保存到: {save_path}")
        except Exception as e:
            logger.error(f"❌ 保存配置失败: {e}")
            raise
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        :return: 配置是否有效
        """
        try:
            # 验证必要的配置项
            required_keys = [
                'basic.data_path',
                'basic.data_provider',
                'time_levels.available_levels'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    logger.error(f"❌ 缺少必要配置: {key}")
                    return False
            
            # 验证时间周期配置
            available_levels = self.get_available_levels()
            default_levels = self.get_default_levels()
            
            for level in default_levels:
                if level not in available_levels:
                    logger.error(f"❌ 无效的默认时间周期: {level}")
                    return False
            
            # 验证数据路径
            data_path = self.get_data_path()
            if not os.path.exists(os.path.dirname(data_path)):
                logger.warning(f"⚠️ 数据路径的父目录不存在: {data_path}")
            
            logger.info("✅ 配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 配置验证失败: {e}")
            return False
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("\n📋 当前配置摘要")
        print("=" * 50)
        print(f"数据存储路径: {self.get_data_path()}")
        print(f"数据提供者: {self.get_data_provider()}")
        print(f"数据库名称: {self.get_db_name()}")
        print(f"可用时间周期: {', '.join(self.get_available_levels())}")
        print(f"默认时间周期: {', '.join(self.get_default_levels())}")
        print(f"更新模式: {self.get_update_mode()}")
        print(f"休眠时间: {self.get_sleeping_time()}秒")
        print(f"强制更新: {'是' if self.get_force_update() else '否'}")
        print(f"日志级别: {self.get_log_level()}")
        print(f"交互式模式: {'启用' if self.is_interactive_enabled() else '禁用'}")
        
        time_range = self.get_time_range()
        print(f"时间范围: {time_range['start_date'] or '自动'} ~ {time_range['end_date']}")
        
        default_codes = self.get_default_codes()
        if default_codes:
            print(f"默认股票: {', '.join(default_codes[:5])}{'...' if len(default_codes) > 5 else ''}")
        else:
            print("默认股票: 全部股票")


# 全局配置管理器实例
_config_manager = None


def get_config_manager(config_file: str = None) -> KlineConfigManager:
    """
    获取全局配置管理器实例
    
    :param config_file: 配置文件路径
    :return: 配置管理器实例
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = KlineConfigManager(config_file)
    return _config_manager


def init_config_manager(config_file: str = None) -> KlineConfigManager:
    """
    初始化配置管理器
    
    :param config_file: 配置文件路径
    :return: 配置管理器实例
    """
    global _config_manager
    _config_manager = KlineConfigManager(config_file)
    return _config_manager
