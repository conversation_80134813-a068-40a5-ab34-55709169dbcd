#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股票列表读取功能
包括QMT数据源、数据库存储、板块分类等功能的测试
"""

import sys
import os
from datetime import datetime
from typing import List, Dict

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_qmt_connection():
    """测试QMT连接"""
    print("1️⃣ 测试QMT连接")
    print("=" * 50)
    
    try:
        import xtquant.xtdata as xt
        
        # 连接QMT
        result = xt.connect()
        if result == 0:
            print("✅ QMT连接成功")
            
            # 获取服务信息
            info = xt.get_client_info()
            print(f"📡 服务信息: {info}")
            
            return True
        else:
            print(f"❌ QMT连接失败，错误代码: {result}")
            return False
            
    except Exception as e:
        print(f"❌ QMT连接异常: {e}")
        return False

def test_get_stock_list():
    """测试获取股票列表"""
    print("\n2️⃣ 测试获取股票列表")
    print("=" * 50)
    
    try:
        import xtquant.xtdata as xt
        
        # 获取所有股票列表
        all_stocks = xt.get_stock_list_in_sector('沪深A股')
        print(f"📊 获取到股票总数: {len(all_stocks)}")
        
        # 显示前10只股票
        print("\n📋 前10只股票示例:")
        for i, stock in enumerate(all_stocks[:10]):
            print(f"  {i+1:2d}. {stock}")
        
        # 按交易所分类统计
        sh_stocks = [s for s in all_stocks if s.endswith('.SH')]
        sz_stocks = [s for s in all_stocks if s.endswith('.SZ')]
        
        print(f"\n📈 交易所分布:")
        print(f"  上海交易所 (SH): {len(sh_stocks)} 只")
        print(f"  深圳交易所 (SZ): {len(sz_stocks)} 只")
        
        return all_stocks
        
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return []

def test_board_classification(stock_list: List[str]):
    """测试板块分类功能"""
    print("\n3️⃣ 测试板块分类功能")
    print("=" * 50)
    
    try:
        # 导入板块分类函数
        from start_kline_recorder import get_board_by_code, get_board_info
        
        # 板块统计
        board_stats = {}
        
        # 分类前50只股票作为示例
        test_stocks = stock_list[:50] if len(stock_list) > 50 else stock_list
        
        print(f"🔍 分析 {len(test_stocks)} 只股票的板块分类:")
        print("-" * 40)
        
        for stock_code in test_stocks:
            board = get_board_by_code(stock_code)
            board_stats[board] = board_stats.get(board, 0) + 1
        
        # 显示统计结果
        print("\n📊 板块分布统计:")
        for board, count in sorted(board_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"  {board:8s}: {count:3d} 只")
        
        # 显示每个板块的示例股票
        print("\n📋 各板块股票示例:")
        board_examples = {}
        for stock_code in test_stocks:
            board = get_board_by_code(stock_code)
            if board not in board_examples:
                board_examples[board] = []
            if len(board_examples[board]) < 3:  # 每个板块显示3个示例
                board_examples[board].append(stock_code)
        
        for board, examples in board_examples.items():
            print(f"  {board}: {', '.join(examples)}")
        
        return board_stats
        
    except Exception as e:
        print(f"❌ 板块分类测试失败: {e}")
        return {}

def test_database_operations():
    """测试数据库操作"""
    print("\n4️⃣ 测试数据库操作")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from start_kline_recorder import (
            setup_database, Stock, get_board_statistics, 
            get_stocks_by_board, create_sample_stocks
        )
        from zvt_data_recorder.database import get_db_session
        
        # 设置数据库
        print("🏗️ 设置数据库...")
        setup_database()
        
        # 获取数据库会话
        session = get_db_session(provider="qmt", data_schema=Stock)
        
        # 检查股票数量
        stock_count = session.query(Stock).count()
        print(f"📊 数据库中股票数量: {stock_count}")
        
        if stock_count == 0:
            print("⚠️ 数据库为空，创建示例股票...")
            create_sample_stocks()
            stock_count = session.query(Stock).count()
            print(f"✅ 已创建 {stock_count} 只股票")
        
        # 测试板块统计
        print("\n📈 数据库板块统计:")
        board_stats = get_board_statistics(session)
        for board, count in board_stats.items():
            print(f"  {board}: {count} 只")
        
        # 测试按板块获取股票
        print("\n🔍 按板块获取股票测试:")
        for board in board_stats.keys():
            stocks = get_stocks_by_board(session, board)
            print(f"  {board}: {len(stocks)} 只股票")
            if stocks:
                # 显示前3只股票的entity_id
                examples = stocks[:3]
                print(f"    示例: {examples}")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False

def test_stock_info_details():
    """测试股票详细信息"""
    print("\n5️⃣ 测试股票详细信息")
    print("=" * 50)
    
    try:
        import xtquant.xtdata as xt
        
        # 测试几只不同板块的股票
        test_codes = [
            "000001.SZ",  # 深市主板
            "002001.SZ",  # 中小板
            "300001.SZ",  # 创业板
            "600000.SH",  # 沪市主板
            "688001.SH",  # 科创板
        ]
        
        print("📋 股票详细信息:")
        print("-" * 60)
        
        for code in test_codes:
            try:
                # 获取股票基本信息
                info = xt.get_instrument_detail(code)
                if info:
                    from start_kline_recorder import get_board_by_code
                    board = get_board_by_code(code)
                    
                    print(f"股票代码: {code}")
                    print(f"股票名称: {info.get('InstrumentName', 'N/A')}")
                    print(f"交易板块: {board}")
                    print(f"交易所: {info.get('ExchangeID', 'N/A')}")
                    print("-" * 30)
                else:
                    print(f"❌ 无法获取 {code} 的详细信息")
                    
            except Exception as e:
                print(f"⚠️ 获取 {code} 信息失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票详细信息测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎯 ZVT股票列表读取测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 测试结果统计
    test_results = {}
    
    # 1. 测试QMT连接
    test_results['QMT连接'] = test_qmt_connection()
    
    # 2. 测试获取股票列表
    stock_list = []
    if test_results['QMT连接']:
        stock_list = test_get_stock_list()
        test_results['股票列表获取'] = len(stock_list) > 0
    else:
        test_results['股票列表获取'] = False
        print("\n⚠️ 跳过股票列表获取测试（QMT连接失败）")
    
    # 3. 测试板块分类
    if stock_list:
        board_stats = test_board_classification(stock_list)
        test_results['板块分类'] = len(board_stats) > 0
    else:
        test_results['板块分类'] = False
        print("\n⚠️ 跳过板块分类测试（无股票数据）")
    
    # 4. 测试数据库操作
    test_results['数据库操作'] = test_database_operations()
    
    # 5. 测试股票详细信息
    if test_results['QMT连接']:
        test_results['股票详细信息'] = test_stock_info_details()
    else:
        test_results['股票详细信息'] = False
        print("\n⚠️ 跳过股票详细信息测试（QMT连接失败）")
    
    # 显示测试总结
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12s}: {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")

if __name__ == "__main__":
    main()
