#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ZVT Data Recorder 基本使用示例

这个示例展示了如何使用ZVT Data Recorder创建数据模型、记录器，并进行数据记录。
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import List

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import pandas as pd
from sqlalchemy import Column, String, Float, DateTime, Integer
from sqlalchemy.orm import declarative_base

# 导入ZVT Data Recorder组件
from zvt_data_recorder.core.schema import Mixin
from zvt_data_recorder.core.recorder import TimeSeriesDataRecorder
from zvt_data_recorder.database.session import get_db_session
from zvt_data_recorder.types.enums import IntervalLevel

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建数据库基类
Base = declarative_base()


# 1. 定义实体模型（股票）
class Stock(Base, Mixin):
    """股票实体模型"""
    __tablename__ = 'stock'

    # 股票特有字段
    code = Column(String(20))  # 股票代码
    name = Column(String(100))  # 股票名称
    market = Column(String(10))  # 市场：SZ, SH
    industry = Column(String(100))  # 行业
    market_cap = Column(Float)  # 市值
    pe_ratio = Column(Float)  # 市盈率


# 2. 定义数据模型（股票价格）
class StockPrice(Base, Mixin):
    """股票价格数据模型"""
    __tablename__ = 'stock_price'
    
    # OHLCV数据
    open = Column(Float)  # 开盘价
    high = Column(Float)  # 最高价
    low = Column(Float)   # 最低价
    close = Column(Float) # 收盘价
    volume = Column(Integer)  # 成交量
    amount = Column(Float)    # 成交额
    
    # 技术指标
    ma5 = Column(Float)   # 5日均线
    ma10 = Column(Float)  # 10日均线
    ma20 = Column(Float)  # 20日均线


# 3. 定义数据记录器
class MockStockPriceRecorder(TimeSeriesDataRecorder):
    """模拟股票价格数据记录器"""
    
    provider = "mock"
    data_schema = StockPrice
    entity_provider = "mock"
    entity_schema = Stock
    
    def record(self, entity, start, end, size, timestamps):
        """
        记录数据的核心方法
        
        :param entity: 实体对象
        :param start: 开始时间
        :param end: 结束时间
        :param size: 数据大小
        :param timestamps: 时间戳列表
        :return: 数据记录列表
        """
        logger.info(f"Recording data for {entity.code} from {start} to {end}")
        
        # 模拟生成股票价格数据
        records = []
        current_date = start if start else datetime.now() - timedelta(days=30)
        end_date = end if end else datetime.now()
        
        # 模拟基础价格
        base_price = 10.0
        
        while current_date <= end_date and len(records) < (size or 30):
            # 生成随机价格数据（简单的随机游走）
            import random
            
            change_pct = random.uniform(-0.05, 0.05)  # ±5%的变化
            base_price *= (1 + change_pct)
            
            open_price = base_price * random.uniform(0.98, 1.02)
            high_price = max(open_price, base_price * random.uniform(1.0, 1.05))
            low_price = min(open_price, base_price * random.uniform(0.95, 1.0))
            close_price = base_price
            volume = random.randint(1000000, 10000000)
            amount = close_price * volume
            
            record = {
                'timestamp': current_date,
                'open': round(open_price, 2),
                'high': round(high_price, 2),
                'low': round(low_price, 2),
                'close': round(close_price, 2),
                'volume': volume,
                'amount': round(amount, 2),
                'ma5': round(close_price * random.uniform(0.98, 1.02), 2),
                'ma10': round(close_price * random.uniform(0.97, 1.03), 2),
                'ma20': round(close_price * random.uniform(0.95, 1.05), 2),
            }
            
            records.append(record)
            current_date += timedelta(days=1)
        
        logger.info(f"Generated {len(records)} records for {entity.code}")
        return records
    
    def get_data_map(self):
        """定义数据字段映射"""
        return {
            'timestamp': ('timestamp', pd.to_datetime),
            'open': ('open', float),
            'high': ('high', float),
            'low': ('low', float),
            'close': ('close', float),
            'volume': ('volume', int),
            'amount': ('amount', float),
            'ma5': ('ma5', float),
            'ma10': ('ma10', float),
            'ma20': ('ma20', float),
        }


def create_sample_stocks():
    """创建示例股票数据"""
    logger.info("Creating sample stocks...")
    
    # 获取数据库会话
    session = get_db_session(provider="mock", data_schema=Stock)
    
    # 创建表
    Base.metadata.create_all(session.bind)
    
    # 示例股票数据
    stocks_data = [
        {
            'id': 'stock_sz_000001',
            'entity_id': 'stock_sz_000001',
            'code': '000001',
            'name': '平安银行',
            'market': 'SZ',
            'industry': '银行',
            'list_date': datetime(1991, 4, 3),
        },
        {
            'id': 'stock_sz_000002',
            'entity_id': 'stock_sz_000002', 
            'code': '000002',
            'name': '万科A',
            'market': 'SZ',
            'industry': '房地产',
            'list_date': datetime(1991, 1, 29),
        },
        {
            'id': 'stock_sh_600000',
            'entity_id': 'stock_sh_600000',
            'code': '600000',
            'name': '浦发银行',
            'market': 'SH', 
            'industry': '银行',
            'list_date': datetime(1999, 11, 10),
        }
    ]
    
    # 插入股票数据
    for stock_data in stocks_data:
        existing = session.query(Stock).filter(Stock.id == stock_data['id']).first()
        if not existing:
            stock = Stock(**stock_data)
            session.add(stock)
    
    session.commit()
    session.close()
    
    logger.info(f"Created {len(stocks_data)} sample stocks")
    return [stock['entity_id'] for stock in stocks_data]


def main():
    """主函数"""
    print("🚀 ZVT Data Recorder 基本使用示例")
    print("=" * 50)
    
    # 1. 初始化配置
    print("\n📋 1. 初始化配置...")
    config = {
        'DATA_PATH': './data',
        'LOG_LEVEL': 'INFO',
        'EMAIL_ENABLED': False,  # 示例中禁用邮件通知
    }
    init_config(config)
    
    # 2. 注册数据模式
    print("\n📊 2. 注册数据模式...")
    register_schema(
        providers=["mock"],
        db_name="stock_data",
        schema_base=Base,
        entity_type="stock"
    )
    
    # 注册提供者到模式
    Stock.register_provider("mock")
    StockPrice.register_provider("mock")
    
    # 注册记录器
    StockPrice.register_recorder_cls("mock", MockStockPriceRecorder)
    
    # 3. 创建示例数据
    print("\n🏗️ 3. 创建示例股票数据...")
    entity_ids = create_sample_stocks()
    
    # 4. 运行数据记录器
    print("\n📈 4. 开始记录股票价格数据...")
    
    try:
        # 方式1：直接使用记录器类
        print("\n方式1：直接使用记录器类")
        recorder = MockStockPriceRecorder(
            entity_ids=entity_ids[:2],  # 只记录前两只股票
            sleeping_time=1,
            force_update=True
        )
        recorder.run()
        
        # 方式2：使用工具函数
        print("\n方式2：使用工具函数")
        run_data_recorder(
            domain=StockPrice,
            data_provider="mock",
            entity_provider="mock",
            entity_ids=[entity_ids[2]],  # 记录第三只股票
            sleeping_time=1
        )
        
        print("\n✅ 数据记录完成！")
        
    except Exception as e:
        logger.error(f"数据记录失败: {e}")
        print(f"\n❌ 数据记录失败: {e}")
        return
    
    # 5. 查询和验证数据
    print("\n🔍 5. 查询记录的数据...")
    
    try:
        # 查询股票数据
        stocks_df = Stock.query_data(provider="mock", return_type="df")
        print(f"\n股票数据 ({len(stocks_df)} 条记录):")
        print(stocks_df[['code', 'name', 'market', 'industry']].to_string())
        
        # 查询价格数据
        prices_df = StockPrice.query_data(
            provider="mock", 
            entity_ids=entity_ids,
            return_type="df",
            limit=10
        )
        print(f"\n价格数据 ({len(prices_df)} 条记录，显示最近10条):")
        if not prices_df.empty:
            print(prices_df[['entity_id', 'timestamp', 'open', 'high', 'low', 'close', 'volume']].to_string())
        else:
            print("没有找到价格数据")
        
    except Exception as e:
        logger.error(f"数据查询失败: {e}")
        print(f"\n❌ 数据查询失败: {e}")
    
    print("\n🎉 示例运行完成！")
    print("\n💡 提示:")
    print("- 数据已保存到 ./data 目录")
    print("- 可以使用 SQLite 工具查看数据库文件")
    print("- 修改配置可以支持 MySQL、PostgreSQL 等数据库")


if __name__ == "__main__":
    main()
