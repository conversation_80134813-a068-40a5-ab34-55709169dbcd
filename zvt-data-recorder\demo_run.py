#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
zvt-data-recorder 项目演示
展示项目的核心功能，不依赖外部数据源
"""

import sys
import os
import logging
from datetime import datetime, timedelta
from typing import List

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from zvt_data_recorder.core.schema import Mixin
from zvt_data_recorder.core.recorder import Recorder
from zvt_data_recorder.database.session import get_db_session
from sqlalchemy import Column, String, Float, DateTime, create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 创建数据库基类
Base = declarative_base()


class DemoStockData(Base, Mixin):
    """演示股票数据模型"""
    
    __tablename__ = 'demo_stock_data'
    
    code = Column(String(20), comment='股票代码')
    name = Column(String(100), comment='股票名称')
    open = Column(Float, comment='开盘价')
    high = Column(Float, comment='最高价')
    low = Column(Float, comment='最低价')
    close = Column(Float, comment='收盘价')
    volume = Column(Float, comment='成交量')
    
    def _validate_business_logic(self) -> bool:
        """重写业务逻辑验证"""
        try:
            # K线数据特定验证
            if self.high and self.low and self.high < self.low:
                logger.warning(f"最高价 {self.high} 小于最低价 {self.low}")
                return False
            
            if self.open and self.high and self.low:
                if not (self.low <= self.open <= self.high):
                    logger.warning(f"开盘价 {self.open} 不在合理范围内")
                    return False
            
            if self.close and self.high and self.low:
                if not (self.low <= self.close <= self.high):
                    logger.warning(f"收盘价 {self.close} 不在合理范围内")
                    return False
            
            if self.volume and self.volume < 0:
                logger.warning(f"成交量不能为负数: {self.volume}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"K线数据业务逻辑验证失败: {e}")
            return False


class DemoRecorder(Recorder):
    """演示记录器"""
    
    provider = "demo"
    data_schema = DemoStockData
    
    def __init__(self, **kwargs):
        # 不调用父类初始化，避免数据库连接问题
        self.provider = "demo"
        self.data_schema = DemoStockData
        self.force_update = kwargs.get('force_update', False)
        self.sleeping_time = kwargs.get('sleeping_time', 0)
        self.ignore_failed = kwargs.get('ignore_failed', True)
        self.return_unfinished = kwargs.get('return_unfinished', False)
        
        # 初始化统计信息
        self.stats = {
            'processed_count': 0,
            'success_count': 0,
            'error_count': 0,
            'skipped_count': 0,
            'total_records': 0,
            'start_time': None,
            'end_time': None,
            'start_memory': None,
            'end_memory': None,
            'peak_memory': None,
            'entities_processed': [],
            'errors': []
        }
        
        # 初始化日志
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        
        # 初始化性能监控
        self._init_performance_monitoring()
    
    def _run_impl(self) -> List[str]:
        """实现具体的记录逻辑"""
        try:
            # 模拟股票列表
            stock_list = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
            
            self.logger.info(f"准备处理 {len(stock_list)} 只股票的数据")
            
            # 使用增强的实体处理方法
            unfinished = self.process_entities_with_stats(
                entities=stock_list,
                process_func=self._process_single_stock,
                progress_interval=2  # 每2只股票报告一次进度
            )
            
            return unfinished
            
        except Exception as e:
            self.logger.error(f"记录器运行失败: {e}")
            raise
    
    def _process_single_stock(self, stock_code: str) -> int:
        """处理单只股票的数据"""
        try:
            import random
            
            # 模拟生成数据
            records = []
            base_price = random.uniform(10.0, 50.0)
            
            for i in range(5):  # 生成5天的数据
                # 生成随机价格数据
                change_pct = random.uniform(-0.05, 0.05)  # ±5%的变化
                base_price *= (1 + change_pct)
                
                open_price = base_price * random.uniform(0.98, 1.02)
                high_price = max(open_price, base_price * random.uniform(1.0, 1.05))
                low_price = min(open_price, base_price * random.uniform(0.95, 1.0))
                close_price = base_price
                volume = random.randint(1000000, 10000000)
                
                # 创建数据对象
                record = DemoStockData()
                record.id = f"{stock_code}_{datetime.now().strftime('%Y%m%d')}_{i}"
                record.entity_id = stock_code
                record.timestamp = datetime.now() - timedelta(days=i)
                record.code = stock_code
                record.name = f"股票{stock_code}"
                record.open = round(open_price, 2)
                record.high = round(high_price, 2)
                record.low = round(low_price, 2)
                record.close = round(close_price, 2)
                record.volume = volume
                
                # 验证和清洗数据
                if record.validate_and_clean():
                    records.append(record)
                    self.logger.debug(f"生成有效记录: {record.code} - {record.close}")
                else:
                    self.logger.warning(f"跳过无效记录: {record.code}")
            
            self.logger.info(f"成功处理 {stock_code}，生成 {len(records)} 条记录")
            return len(records)
            
        except Exception as e:
            self.logger.error(f"处理股票 {stock_code} 失败: {e}")
            raise


def main():
    """主演示函数"""
    print("🚀 zvt-data-recorder 项目演示")
    print("=" * 50)
    
    try:
        logger.info("开始演示数据记录功能")
        
        # 1. 演示数据验证功能
        print("\n📊 1. 演示数据验证功能")
        
        # 创建测试数据
        test_record = DemoStockData()
        test_record.id = "test_001"
        test_record.entity_id = "000001.SZ"
        test_record.timestamp = datetime.now()
        test_record.code = "000001.SZ"
        test_record.name = "平安银行"
        test_record.open = 10.0
        test_record.high = 11.0
        test_record.low = 9.5
        test_record.close = 10.5
        test_record.volume = 1000000
        
        if test_record.validate_and_clean():
            print("✅ 数据验证通过")
            print(f"   股票代码: {test_record.code}")
            print(f"   开盘价: {test_record.open}")
            print(f"   收盘价: {test_record.close}")
        else:
            print("❌ 数据验证失败")
        
        # 2. 演示记录器功能
        print("\n📈 2. 演示记录器功能")
        
        # 创建记录器
        recorder = DemoRecorder(
            force_update=False,
            sleeping_time=0,  # 演示时不休眠
            ignore_failed=True,
            return_unfinished=True
        )
        
        # 运行记录器
        unfinished = recorder.run()
        
        # 3. 展示统计信息
        print("\n📊 3. 统计信息")
        stats = recorder.get_stats_summary()
        
        print(f"处理实体数: {stats['processed_count']}")
        print(f"成功数: {stats['success_count']}")
        print(f"失败数: {stats['error_count']}")
        print(f"跳过数: {stats['skipped_count']}")
        print(f"总记录数: {stats['total_records']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        
        if stats.get('elapsed_time'):
            print(f"总耗时: {stats['elapsed_time']:.2f}秒")
            print(f"吞吐量: {stats.get('throughput', 0):.1f}条/秒")
        
        if stats.get('memory_delta'):
            print(f"内存变化: {stats['memory_delta']:+.1f}MB")
        
        if unfinished:
            print(f"未完成的实体: {unfinished}")
        
        # 关闭记录器
        recorder.close()
        
        print("\n🎉 演示完成！")
        print("\n💡 项目特点:")
        print("- ✅ 完整的数据验证和清洗机制")
        print("- ✅ 详细的统计信息跟踪")
        print("- ✅ 性能监控和内存管理")
        print("- ✅ 错误处理和重试机制")
        print("- ✅ 纯本地数据处理，无API依赖")
        
        return True
        
    except Exception as e:
        logger.error(f"演示运行失败: {e}")
        print(f"\n❌ 演示失败: {e}")
        return False


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
