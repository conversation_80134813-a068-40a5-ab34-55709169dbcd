# ZVT Data Recorder Dependencies
# 基于ZVT项目提取的数据记录库依赖

# 核心依赖
requests>=2.31.0
SQLAlchemy>=2.0.0,<3.0.0
pandas>=2.0.0
arrow>=1.2.0

# 数据库驱动
# SQLite (内置支持)
# MySQL (可选)
# PyMySQL>=1.0.0
# PostgreSQL (可选)  
# psycopg2-binary>=2.9.0

# 数据处理
numpy>=1.24.0
openpyxl>=3.1.0

# 时间处理
pytz>=2023.3

# 日志和配置
pydantic>=2.0.0

# 开发和测试依赖 (可选)
pytest>=7.0.0
pytest-cov>=4.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# 文档生成 (可选)
sphinx>=6.0.0
sphinx-rtd-theme>=1.2.0
