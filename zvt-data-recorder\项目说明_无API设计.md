# zvt-data-recorder 项目说明 - 纯本地数据处理设计

## 🎯 项目定位

**zvt-data-recorder** 是一个专注于**本地数据处理和存储**的金融数据记录系统，**不包含任何Web API或网络接口功能**。

## ❌ 不包含的功能

### 明确排除的API相关功能：
- ❌ **Web API接口** - 不提供HTTP REST API
- ❌ **API服务器** - 不启动任何Web服务
- ❌ **网络接口** - 不对外提供网络访问接口
- ❌ **远程调用** - 不支持远程过程调用
- ❌ **Web框架** - 不使用Flask/Django等Web框架

## ✅ 核心功能定位

### 1. 本地数据采集
```python
# QMT本地客户端数据采集（不是Web API）
class QmtKdataRecorder:
    """
    通过QMT本地客户端软件采集数据
    - 连接本地QMT客户端程序
    - 读取本地数据源
    - 无网络通信
    """
```

### 2. 本地数据存储
```python
# 本地数据库存储
class Recorder:
    """
    将数据存储到本地数据库
    - SQLite/MySQL等本地数据库
    - 本地文件系统存储
    - 无远程数据传输
    """
```

### 3. 本地数据处理
```python
# 本地数据验证和清洗
class Mixin:
    """
    本地数据处理功能
    - 数据验证
    - 数据清洗
    - 格式转换
    """
```

## 🏗️ 架构设计原则

### 纯本地处理架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   QMT客户端     │───▶│  数据记录器     │───▶│   本地数据库    │
│  (本地软件)     │    │ (本地处理)      │    │  (本地存储)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        ▲                       ▲                       ▲
        │                       │                       │
    本地连接              本地数据处理            本地文件系统
   (无网络通信)           (无网络传输)           (无远程访问)
```

### 数据流向
1. **数据源** → QMT本地客户端软件
2. **数据采集** → 通过xtquant本地接口读取
3. **数据处理** → 本地验证、清洗、转换
4. **数据存储** → 保存到本地数据库
5. **数据查询** → 直接查询本地数据库

## 📁 项目结构说明

```
zvt-data-recorder/
├── src/zvt_data_recorder/
│   ├── core/                    # 核心本地处理功能
│   │   ├── schema.py           # 数据模型（本地）
│   │   ├── recorder.py         # 记录器基类（本地）
│   │   └── service.py          # 本地服务
│   ├── database/               # 本地数据库管理
│   │   ├── session.py          # 数据库会话（本地）
│   │   └── engine.py           # 数据库引擎（本地）
│   ├── recorders/              # 数据记录器（本地采集）
│   │   ├── qmt_recorder.py     # QMT本地客户端记录器
│   │   └── __init__.py
│   └── utils/                  # 工具函数（本地处理）
├── examples/                   # 使用示例（本地运行）
├── tests/                      # 单元测试（本地测试）
└── docs/                       # 文档
```

**注意**：项目中没有任何`api/`、`web/`、`server/`等目录，确保不包含API相关功能。

## 🔧 使用方式

### 典型使用场景
```python
# 1. 本地数据采集
recorder = QmtKdataRecorder(level='1d')
recorder.run()  # 本地运行，无网络通信

# 2. 本地数据查询
session = get_db_session()  # 本地数据库会话
data = session.query(StockKlineDay).all()  # 查询本地数据

# 3. 本地数据处理
for record in data:
    if record.validate_and_clean():  # 本地验证
        # 处理有效数据
        process_data(record)
```

### 部署方式
```bash
# 本地安装和运行
pip install -e .
python examples/qmt_kdata_example.py  # 本地运行脚本

# 无需启动服务器
# 无需配置网络端口
# 无需处理HTTP请求
```

## 🎯 与API系统的区别

| 特性 | zvt-data-recorder | 传统API系统 |
|------|-------------------|-------------|
| **网络通信** | ❌ 无 | ✅ HTTP/REST |
| **服务器** | ❌ 无 | ✅ Web服务器 |
| **端口监听** | ❌ 无 | ✅ 监听端口 |
| **远程访问** | ❌ 无 | ✅ 远程调用 |
| **数据传输** | ❌ 本地 | ✅ 网络传输 |
| **部署复杂度** | ✅ 简单 | ❌ 复杂 |
| **安全考虑** | ✅ 本地安全 | ❌ 网络安全 |

## 📊 技术栈

### 使用的技术
- ✅ **SQLAlchemy** - 本地数据库ORM
- ✅ **Pandas** - 本地数据处理
- ✅ **xtquant** - QMT本地客户端接口
- ✅ **logging** - 本地日志记录
- ✅ **unittest** - 本地单元测试

### 不使用的技术
- ❌ **Flask/Django** - Web框架
- ❌ **FastAPI** - API框架
- ❌ **requests** - HTTP客户端
- ❌ **aiohttp** - 异步HTTP
- ❌ **gunicorn/uwsgi** - Web服务器

## 🚀 优势

### 1. 简单性
- 无需配置Web服务器
- 无需处理HTTP协议
- 无需考虑网络安全
- 直接本地运行

### 2. 性能
- 无网络延迟
- 直接内存访问
- 本地数据库查询
- 无序列化开销

### 3. 安全性
- 无网络暴露
- 本地数据访问
- 无远程攻击面
- 简单权限控制

### 4. 可靠性
- 无网络故障
- 无服务器宕机
- 本地数据一致性
- 简单错误处理

## 📝 总结

**zvt-data-recorder** 是一个专注于本地数据处理的系统：

- 🎯 **定位明确**：纯本地数据记录和处理
- 🚫 **无API设计**：不包含任何Web API功能
- 🔧 **简单易用**：直接本地运行，无需服务器
- 📊 **功能完整**：数据采集、验证、存储、查询
- 🛡️ **安全可靠**：本地处理，无网络风险

这种设计确保了系统的简单性、可靠性和安全性，完全符合您"不要包含API"的要求。
