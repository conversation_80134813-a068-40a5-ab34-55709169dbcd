# -*- coding: utf-8 -*-
"""
数据处理工具

从ZVT项目中提取的数据处理相关工具函数。
"""

import logging
import numbers
from decimal import Decimal, getcontext
from typing import Dict, Any

import pandas as pd

# 设置精度
getcontext().prec = 16

logger = logging.getLogger(__name__)

# 空值定义
none_values = ["不变", "--", "-", "新进"]
zero_values = ["不变", "--", "-", "新进"]


def pd_is_not_null(df):
    """
    判断DataFrame是否不为空
    
    :param df: DataFrame
    :return: 是否不为空
    """
    return df is not None and not df.empty


def index_df(df, index=None, time_field="timestamp"):
    """
    为DataFrame设置索引
    
    :param df: DataFrame
    :param index: 索引列名
    :param time_field: 时间字段名
    :return: 设置索引后的DataFrame
    """
    if df is None or df.empty:
        return df
    
    if index is None:
        index = ["entity_id", time_field]
    
    if isinstance(index, str):
        index = [index]
    
    # 检查索引列是否存在
    existing_cols = [col for col in index if col in df.columns]
    if existing_cols:
        df = df.set_index(existing_cols)
    
    return df


def fill_domain_from_dict(domain_obj, data_dict: Dict[str, Any], data_map: Dict[str, tuple] = None):
    """
    从字典填充领域对象
    
    :param domain_obj: 领域对象
    :param data_dict: 数据字典
    :param data_map: 数据映射，格式为 {'original_field': ('domain_field', transform_func)}
    """
    if not data_dict:
        return
    
    if data_map is None:
        data_map = {}
    
    for original_field, value in data_dict.items():
        if original_field in data_map:
            domain_field, transform_func = data_map[original_field]
            if transform_func:
                try:
                    value = transform_func(value)
                except Exception as e:
                    logger.warning(f"Transform failed for {original_field}: {e}")
                    continue
        else:
            domain_field = original_field
        
        # 设置属性值
        if hasattr(domain_obj, domain_field):
            setattr(domain_obj, domain_field, value)


def to_float(value):
    """
    转换为浮点数
    
    :param value: 输入值
    :return: 浮点数或None
    """
    if value is None:
        return None
    
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        value = value.strip()
        if value in none_values:
            return None
        if value in zero_values:
            return 0.0
        
        try:
            # 处理百分比
            if value.endswith('%'):
                return float(value[:-1]) / 100.0
            
            # 处理中文数字单位
            if value.endswith('万'):
                return float(value[:-1]) * 10000
            if value.endswith('亿'):
                return float(value[:-1]) * 100000000
            
            return float(value)
        except ValueError:
            logger.warning(f"Cannot convert to float: {value}")
            return None
    
    return None


def to_int(value):
    """
    转换为整数
    
    :param value: 输入值
    :return: 整数或None
    """
    float_val = to_float(value)
    if float_val is not None:
        return int(float_val)
    return None


def to_decimal(value):
    """
    转换为Decimal
    
    :param value: 输入值
    :return: Decimal或None
    """
    if value is None:
        return None
    
    try:
        return Decimal(str(value))
    except:
        return None


def first_item_to_float(the_list):
    """获取列表第一个元素并转换为浮点数"""
    if the_list and len(the_list) > 0:
        return to_float(the_list[0])
    return None


def second_item_to_float(the_list):
    """获取列表第二个元素并转换为浮点数"""
    if the_list and len(the_list) > 1:
        return to_float(the_list[1])
    return None


def add_func_to_value(the_map, the_func):
    """
    为映射字典的值添加转换函数
    
    :param the_map: 映射字典
    :param the_func: 转换函数
    :return: 更新后的映射字典
    """
    for k, v in the_map.items():
        the_map[k] = (v, the_func)
    return the_map


def normalize_data(df, columns=None):
    """
    数据标准化
    
    :param df: DataFrame
    :param columns: 要标准化的列名列表
    :return: 标准化后的DataFrame
    """
    if df is None or df.empty:
        return df
    
    if columns is None:
        # 选择数值列
        columns = df.select_dtypes(include=[numbers.Number]).columns
    
    df_normalized = df.copy()
    for col in columns:
        if col in df.columns:
            mean_val = df[col].mean()
            std_val = df[col].std()
            if std_val != 0:
                df_normalized[col] = (df[col] - mean_val) / std_val
    
    return df_normalized


def clean_data(df, drop_na=True, fill_value=None):
    """
    数据清洗
    
    :param df: DataFrame
    :param drop_na: 是否删除空值行
    :param fill_value: 填充值
    :return: 清洗后的DataFrame
    """
    if df is None or df.empty:
        return df
    
    df_clean = df.copy()
    
    if drop_na:
        df_clean = df_clean.dropna()
    elif fill_value is not None:
        df_clean = df_clean.fillna(fill_value)
    
    return df_clean


def group_by_entity_id(df, func=None):
    """
    按entity_id分组
    
    :param df: DataFrame
    :param func: 聚合函数
    :return: 分组结果
    """
    if df is None or df.empty or 'entity_id' not in df.columns:
        return df
    
    grouped = df.groupby('entity_id')
    
    if func:
        return grouped.apply(func)
    
    return grouped


def normalize_group_compute_result(df):
    """
    标准化分组计算结果
    
    :param df: DataFrame
    :return: 标准化后的DataFrame
    """
    if df is None or df.empty:
        return df
    
    # 重置索引
    if df.index.nlevels > 1:
        df = df.reset_index()
    
    return df


__all__ = [
    # 常量
    "none_values",
    "zero_values",
    
    # DataFrame处理
    "pd_is_not_null",
    "index_df",
    "normalize_group_compute_result",
    "group_by_entity_id",
    
    # 数据填充和转换
    "fill_domain_from_dict",
    "to_float",
    "to_int", 
    "to_decimal",
    "first_item_to_float",
    "second_item_to_float",
    "add_func_to_value",
    
    # 数据处理
    "normalize_data",
    "clean_data",
    "clean_data_frame",
    "pd_is_not_null",
]


def pd_is_not_null(df):
    """
    检查DataFrame是否不为空

    :param df: DataFrame对象
    :return: 是否不为空
    """
    return df is not None and not df.empty


def clean_data_frame(df):
    """
    清洗DataFrame数据

    :param df: 要清洗的DataFrame
    :return: 清洗后的DataFrame
    """
    if df is None or df.empty:
        return df

    # 移除重复行
    df = df.drop_duplicates()

    # 重置索引
    df = df.reset_index(drop=True)

    return df
