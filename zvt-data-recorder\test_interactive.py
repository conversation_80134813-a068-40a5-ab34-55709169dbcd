#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试交互式模式的脚本
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from kline_config_manager import init_config_manager
from update_mode_manager import UpdateModeManager
from interactive_mode import InteractiveMode

def test_interactive_mode():
    """测试交互式模式"""
    print("🧪 测试交互式模式")
    print("=" * 50)
    
    try:
        # 初始化配置管理器
        config_manager = init_config_manager()
        
        # 初始化更新模式管理器
        update_manager = UpdateModeManager(config_manager)
        
        # 初始化交互式模式
        interactive = InteractiveMode(config_manager, update_manager)
        
        print("✅ 所有模块初始化成功")
        print("\n📋 配置管理器测试:")
        config_manager.print_config_summary()
        
        print("\n📋 更新模式管理器测试:")
        # 测试自动模式判断
        mode = update_manager.determine_update_mode('1d', ['stock_sh_600000'])
        print(f"自动判断的更新模式: {mode}")
        
        # 测试时间范围获取
        start_date, end_date = update_manager.get_update_time_range(
            mode, '1d', ['stock_sh_600000']
        )
        print(f"时间范围: {start_date} ~ {end_date}")
        
        # 测试参数验证
        is_valid = update_manager.validate_update_parameters(
            'auto', ['1d'], '2024-01-01', '2024-12-31'
        )
        print(f"参数验证结果: {is_valid}")
        
        print("\n✅ 所有测试通过！")
        print("\n💡 要测试完整的交互式模式，请运行:")
        print("python start_kline_recorder.py --interactive")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_interactive_mode()
