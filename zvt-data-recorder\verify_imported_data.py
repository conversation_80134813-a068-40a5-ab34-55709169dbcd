#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证导入数据的脚本

检查从CSV文件导入的股票数据是否正确存储在数据库中。
验证数据完整性和准确性。
"""

import os
import sys
import logging
import pandas as pd
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import Column, String, Float, DateTime
from sqlalchemy.orm import declarative_base

from zvt_data_recorder import (
    Mixin, TradableEntity, get_db_session
)
from zvt_data_recorder.database import register_schema
from zvt_data_recorder.config import init_config, init_logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建数据库基类
Base = declarative_base()


class Stock(Base, TradableEntity):
    """股票实体模型"""
    __tablename__ = 'stock'
    
    exchange = Column(String(10))      # 交易所
    sector = Column(String(100))       # 板块
    industry = Column(String(100))     # 行业
    market_cap = Column(Float)         # 市值
    list_date = Column(DateTime)       # 上市日期
    delist_date = Column(DateTime)     # 退市日期


class StockKlineDay(Base, Mixin):
    """股票日K线数据"""
    __tablename__ = 'stock_kline_day'
    
    # OHLCV数据
    open = Column(Float)               # 开盘价
    high = Column(Float)               # 最高价
    low = Column(Float)                # 最低价
    close = Column(Float)              # 收盘价
    volume = Column(Float)             # 成交量
    turnover = Column(Float)           # 成交额
    
    # 其他数据
    pre_close = Column(Float)          # 前收盘价
    change_pct = Column(Float)         # 涨跌幅
    market_cap = Column(Float)         # 流通市值
    total_market_cap = Column(Float)   # 总市值


def setup_database():
    """设置数据库连接"""
    logger.info("📊 设置数据库连接...")
    
    # 注册数据模式
    providers = ["csv"]
    
    register_schema(
        providers=providers,
        db_name="stock_csv_data",
        schema_base=Base,
        entity_type="stock"
    )
    
    # 注册提供者到各个模式
    for schema_cls in [Stock, StockKlineDay]:
        for provider in providers:
            schema_cls.register_provider(provider)
    
    logger.info("✅ 数据库连接设置完成")


def verify_stock_data():
    """验证股票基础信息"""
    logger.info("🔍 验证股票基础信息...")
    
    session = get_db_session(provider="csv", data_schema=Stock)
    stocks = session.query(Stock).all()
    
    logger.info(f"📊 数据库中共有 {len(stocks)} 只股票:")
    
    for stock in stocks:
        logger.info(f"  股票代码: {stock.code}")
        logger.info(f"  股票名称: {stock.name}")
        logger.info(f"  交易所: {stock.exchange}")
        logger.info(f"  实体ID: {stock.id}")
        logger.info(f"  创建时间: {stock.timestamp}")
        logger.info("  " + "-" * 40)
    
    session.close()
    return stocks


def verify_kline_data():
    """验证K线数据"""
    logger.info("🔍 验证K线数据...")
    
    session = get_db_session(provider="csv", data_schema=StockKlineDay)
    
    # 获取股票列表
    stock_session = get_db_session(provider="csv", data_schema=Stock)
    stocks = stock_session.query(Stock).all()
    stock_session.close()
    
    for stock in stocks:
        logger.info(f"📈 验证股票 {stock.code} ({stock.name}) 的K线数据:")
        
        # 查询该股票的K线数据
        klines = session.query(StockKlineDay).filter(
            StockKlineDay.entity_id == stock.id
        ).order_by(StockKlineDay.timestamp).all()
        
        if not klines:
            logger.warning(f"  ⚠️ 未找到K线数据")
            continue
        
        logger.info(f"  📊 K线记录数: {len(klines)}")
        logger.info(f"  📅 时间范围: {klines[0].timestamp.date()} ~ {klines[-1].timestamp.date()}")
        
        # 验证数据完整性
        logger.info("  🔍 数据完整性检查:")
        
        # 检查是否有空值
        null_counts = {}
        for field in ['open', 'high', 'low', 'close', 'volume', 'turnover']:
            null_count = sum(1 for k in klines if getattr(k, field) is None)
            null_counts[field] = null_count
            if null_count > 0:
                logger.warning(f"    ⚠️ {field} 字段有 {null_count} 个空值")
        
        if all(count == 0 for count in null_counts.values()):
            logger.info("    ✅ 主要字段无空值")
        
        # 检查价格逻辑
        invalid_price_count = 0
        for kline in klines:
            if (kline.high < kline.low or 
                kline.open < 0 or kline.high < 0 or 
                kline.low < 0 or kline.close < 0):
                invalid_price_count += 1
        
        if invalid_price_count > 0:
            logger.warning(f"    ⚠️ 发现 {invalid_price_count} 条价格逻辑异常的记录")
        else:
            logger.info("    ✅ 价格数据逻辑正常")
        
        # 显示最近几条数据
        logger.info("  📋 最近5条K线数据:")
        recent_klines = klines[-5:]
        for kline in recent_klines:
            logger.info(f"    {kline.timestamp.date()}: "
                       f"开{kline.open:.2f} 高{kline.high:.2f} "
                       f"低{kline.low:.2f} 收{kline.close:.2f} "
                       f"量{kline.volume:.0f}")
        
        logger.info("  " + "=" * 50)
    
    session.close()


def compare_with_csv():
    """与原始CSV文件对比验证"""
    logger.info("🔍 与原始CSV文件对比验证...")
    
    csv_files = {
        "sz002436.csv": "stock_sz_002436",
        "sz002435.csv": "stock_sz_002435"
    }
    
    session = get_db_session(provider="csv", data_schema=StockKlineDay)
    
    for csv_file, entity_id in csv_files.items():
        logger.info(f"📊 验证文件: {csv_file}")
        
        # 读取CSV文件
        try:
            df_csv = pd.read_csv(csv_file, encoding='gbk', skiprows=1)
            logger.info(f"  CSV文件记录数: {len(df_csv)}")
        except Exception as e:
            logger.error(f"  ❌ 读取CSV文件失败: {e}")
            continue
        
        # 查询数据库中的数据
        db_klines = session.query(StockKlineDay).filter(
            StockKlineDay.entity_id == entity_id
        ).count()
        
        logger.info(f"  数据库记录数: {db_klines}")
        
        # 比较记录数
        if len(df_csv) == db_klines:
            logger.info("  ✅ 记录数匹配")
        else:
            logger.warning(f"  ⚠️ 记录数不匹配，差异: {abs(len(df_csv) - db_klines)}")
        
        # 抽样验证几条数据
        if len(df_csv) > 0:
            sample_rows = df_csv.sample(min(3, len(df_csv)))
            logger.info("  🔍 抽样验证:")
            
            for _, row in sample_rows.iterrows():
                date_str = row['交易日期']
                db_record = session.query(StockKlineDay).filter(
                    StockKlineDay.entity_id == entity_id,
                    StockKlineDay.timestamp == pd.to_datetime(date_str)
                ).first()
                
                if db_record:
                    csv_close = float(row['收盘价'])
                    db_close = float(db_record.close)
                    if abs(csv_close - db_close) < 0.01:
                        logger.info(f"    ✅ {date_str}: 收盘价匹配 ({csv_close})")
                    else:
                        logger.warning(f"    ⚠️ {date_str}: 收盘价不匹配 CSV:{csv_close} DB:{db_close}")
                else:
                    logger.warning(f"    ⚠️ {date_str}: 数据库中未找到对应记录")
    
    session.close()


def main():
    """主函数"""
    try:
        logger.info("🎯 开始验证导入的数据")
        
        # 初始化配置
        init_config()
        init_logging()
        
        # 设置数据库
        setup_database()
        
        # 验证股票基础信息
        stocks = verify_stock_data()
        
        if not stocks:
            logger.error("❌ 未找到任何股票数据，请先运行导入脚本")
            return
        
        # 验证K线数据
        verify_kline_data()
        
        # 与CSV文件对比
        compare_with_csv()
        
        logger.info("🎉 数据验证完成!")
        
    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
