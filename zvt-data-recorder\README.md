# ZVT Data Recorder

[![Python Version](https://img.shields.io/badge/python-3.9%2B-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![PyPI Version](https://img.shields.io/pypi/v/zvt-data-recorder.svg)](https://pypi.org/project/zvt-data-recorder/)

**ZVT Data Recorder** 是从 [ZVT项目](https://github.com/zvtvz/zvt) 中提取的独立数据记录库，提供了完整的数据记录、存储和管理能力。

## ✨ 特性

- 🚀 **高性能数据记录**: 支持多种数据源的高效数据记录
- 💾 **灵活的数据存储**: 基于SQLAlchemy，支持SQLite、MySQL、PostgreSQL等数据库
- 🔄 **增量更新**: 智能的增量数据更新机制，避免重复记录
- 📊 **时间序列支持**: 专为时间序列数据设计的记录器
- 🛠️ **可扩展架构**: 易于扩展的插件式架构
- 📧 **通知系统**: 内置邮件通知功能
- 🔧 **状态管理**: 完善的记录器状态管理和持久化
- 📈 **数据验证**: 内置数据验证和清洗功能

## 🚀 快速开始

### 安装

```bash
# 基础安装
pip install zvt-data-recorder

# 安装MySQL支持
pip install zvt-data-recorder[mysql]

# 安装PostgreSQL支持  
pip install zvt-data-recorder[postgresql]

# 安装所有可选依赖
pip install zvt-data-recorder[all]
```

### 基本使用

```python
from zvt_data_recorder import (
    Mixin, TradableEntity, Recorder, 
    TimeSeriesDataRecorder, get_db_session
)
from zvt_data_recorder.types import IntervalLevel
from sqlalchemy import Column, String, Float, DateTime
from sqlalchemy.orm import declarative_base

# 1. 定义数据模型
Base = declarative_base()

class Stock(Base, TradableEntity):
    __tablename__ = 'stock'
    
    # 股票特有字段
    market_cap = Column(Float)
    pe_ratio = Column(Float)

class StockPrice(Base, Mixin):
    __tablename__ = 'stock_price'
    
    # 价格数据字段
    open = Column(Float)
    high = Column(Float) 
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)

# 2. 定义数据记录器
class StockPriceRecorder(TimeSeriesDataRecorder):
    provider = "example"
    data_schema = StockPrice
    entity_provider = "example"
    entity_schema = Stock
    
    def record(self, entity, start, end, size, timestamps):
        # 实现具体的数据获取逻辑
        # 这里应该从数据源获取数据
        data = []
        # ... 获取数据的代码 ...
        return data

# 3. 注册和使用
from zvt_data_recorder.database import register_schema

# 注册数据模式
register_schema(
    providers=["example"],
    db_name="stock_data", 
    schema_base=Base
)

# 使用记录器
recorder = StockPriceRecorder(
    entity_ids=["stock_sz_000001"],
    sleeping_time=1
)
recorder.run()
```

## 📖 核心概念

### 数据模型 (Schema)

- **Mixin**: 所有数据模型的基类，提供基础字段和方法
- **TradableEntity**: 可交易实体基类，用于股票、期货等金融工具
- **自定义模型**: 继承基类创建特定的数据模型

### 记录器 (Recorder)

- **Recorder**: 基础记录器类
- **EntityEventRecorder**: 实体事件记录器
- **TimeSeriesDataRecorder**: 时间序列数据记录器
- **FixedCycleDataRecorder**: 固定周期数据记录器

### 数据库管理

- **引擎管理**: 自动管理数据库连接和引擎
- **会话管理**: 提供数据库会话的创建和管理
- **注册机制**: 灵活的数据模式和提供者注册系统

## 🔧 配置

### 环境变量配置

```bash
# 数据路径
export ZVT_DATA_PATH="/path/to/data"

# 数据库配置
export ZVT_DB_ENGINE_TYPE="sqlite"
export ZVT_DB_ECHO="false"

# 邮件通知配置
export ZVT_EMAIL_ENABLED="true"
export ZVT_EMAIL_SMTP_SERVER="smtp.gmail.com"
export ZVT_EMAIL_SMTP_PORT="587"
export ZVT_EMAIL_USERNAME="<EMAIL>"
export ZVT_EMAIL_PASSWORD="your-password"
export ZVT_EMAIL_FROM="<EMAIL>"
export ZVT_EMAIL_TO="<EMAIL>"
```

### 代码配置

```python
from zvt_data_recorder.config import init_config, init_logging

# 初始化配置
config = {
    'DATA_PATH': '/path/to/data',
    'DB_ENGINE_TYPE': 'sqlite',
    'EMAIL_ENABLED': True,
    'EMAIL_SMTP_SERVER': 'smtp.gmail.com',
    # ... 其他配置
}

init_config(config)
init_logging()
```

## 📚 高级用法

### 自定义记录器

```python
class CustomDataRecorder(TimeSeriesDataRecorder):
    provider = "custom"
    data_schema = YourDataSchema
    entity_provider = "custom"
    entity_schema = YourEntitySchema
    
    def record(self, entity, start, end, size, timestamps):
        # 实现自定义数据获取逻辑
        url = f"https://api.example.com/data/{entity.code}"
        response = self.http_session.get(url)
        data = response.json()
        
        # 转换数据格式
        records = []
        for item in data:
            record = {
                'timestamp': item['date'],
                'value': item['price'],
                # ... 其他字段
            }
            records.append(record)
        
        return records
    
    def get_data_map(self):
        # 定义字段映射
        return {
            'date': ('timestamp', lambda x: pd.to_datetime(x)),
            'price': ('value', float),
        }
```

### 批量数据记录

```python
from zvt_data_recorder.utils import batch_record_data

# 批量记录多个数据类型
domains = [StockPrice, StockVolume, StockIndicator]
results = batch_record_data(
    domains=domains,
    provider="example",
    entity_ids=["stock_sz_000001", "stock_sz_000002"],
    sleeping_time=1
)

print(f"成功: {results['success_count']}, 失败: {results['failed_count']}")
```

### 状态管理

```python
from zvt_data_recorder.core import OneStateService

class MyService(OneStateService):
    state_schema = MyStateSchema
    
    def __init__(self):
        super().__init__()
        if self.state is None:
            self.state = {'last_update': None, 'count': 0}
    
    def update_state(self, new_data):
        self.state['last_update'] = pd.Timestamp.now()
        self.state['count'] += len(new_data)
        self.persist_state()
```

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_recorder.py

# 运行测试并生成覆盖率报告
pytest --cov=zvt_data_recorder --cov-report=html
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎贡献代码！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

## 📞 支持

- 📖 [文档](https://zvt-data-recorder.readthedocs.io/)
- 🐛 [问题反馈](https://github.com/your-username/zvt-data-recorder/issues)
- 💬 [讨论区](https://github.com/your-username/zvt-data-recorder/discussions)

## 🙏 致谢

本项目基于 [ZVT](https://github.com/zvtvz/zvt) 项目的数据记录功能提取而来，感谢ZVT项目的贡献者们。

---

**ZVT Data Recorder** - 让数据记录变得简单而强大！ 🚀
