#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
CSV股票数据导入脚本

用于将CSV格式的股票K线数据导入到zvt数据库系统中。
支持导入股票基础信息和K线数据。
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import Column, String, Float, DateTime
from sqlalchemy.orm import declarative_base

from zvt_data_recorder import (
    Mixin, TradableEntity, get_db_session
)
from zvt_data_recorder.database import register_schema
from zvt_data_recorder.config import init_config, init_logging
from zvt_data_recorder.utils.csv_importer import import_stock_kline_csv

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建数据库基类
Base = declarative_base()


class Stock(Base, TradableEntity):
    """股票实体模型"""
    __tablename__ = 'stock'
    
    exchange = Column(String(10))      # 交易所
    sector = Column(String(100))       # 板块
    industry = Column(String(100))     # 行业
    market_cap = Column(Float)         # 市值
    list_date = Column(DateTime)       # 上市日期
    delist_date = Column(DateTime)     # 退市日期


class StockKlineDay(Base, Mixin):
    """股票日K线数据"""
    __tablename__ = 'stock_kline_day'
    
    # OHLCV数据
    open = Column(Float)               # 开盘价
    high = Column(Float)               # 最高价
    low = Column(Float)                # 最低价
    close = Column(Float)              # 收盘价
    volume = Column(Float)             # 成交量
    turnover = Column(Float)           # 成交额
    
    # 其他数据
    pre_close = Column(Float)          # 前收盘价
    change_pct = Column(Float)         # 涨跌幅
    market_cap = Column(Float)         # 流通市值
    total_market_cap = Column(Float)   # 总市值


def setup_database():
    """设置数据库和注册模式"""
    logger.info("📊 设置数据库和注册模式...")
    
    # 注册数据模式
    providers = ["csv"]
    
    register_schema(
        providers=providers,
        db_name="stock_csv_data",
        schema_base=Base,
        entity_type="stock"
    )
    
    # 注册提供者到各个模式
    for schema_cls in [Stock, StockKlineDay]:
        for provider in providers:
            schema_cls.register_provider(provider)
    
    # 创建数据库表
    stock_session = get_db_session(provider="csv", data_schema=Stock)
    Base.metadata.create_all(stock_session.bind)
    stock_session.close()
    
    kline_session = get_db_session(provider="csv", data_schema=StockKlineDay)
    Base.metadata.create_all(kline_session.bind)
    kline_session.close()
    
    logger.info("✅ 数据库设置完成")


def import_csv_files(csv_files):
    """导入CSV文件"""
    logger.info(f"🚀 开始导入CSV文件: {csv_files}")
    
    try:
        # 导入数据
        results = import_stock_kline_csv(
            csv_files=csv_files,
            stock_schema=Stock,
            kline_schema=StockKlineDay,
            provider="csv"
        )
        
        # 打印结果
        logger.info("📈 导入结果统计:")
        logger.info(f"  处理文件数: {results['files_processed']}")
        logger.info(f"  导入股票数: {results['total_stocks']}")
        logger.info(f"  导入K线数: {results['total_klines']}")
        
        if results['errors']:
            logger.warning(f"  错误数量: {len(results['errors'])}")
            for error in results['errors']:
                logger.error(f"    {error}")
        
        return results
        
    except Exception as e:
        logger.error(f"❌ 导入失败: {e}")
        raise


def query_imported_data():
    """查询导入的数据"""
    logger.info("🔍 查询导入的数据...")
    
    try:
        # 查询股票信息
        stock_session = get_db_session(provider="csv", data_schema=Stock)
        stocks = stock_session.query(Stock).all()
        
        logger.info(f"📊 股票信息 (共{len(stocks)}只):")
        for stock in stocks:
            logger.info(f"  {stock.code} - {stock.name} ({stock.exchange})")
        
        stock_session.close()
        
        # 查询K线数据统计
        kline_session = get_db_session(provider="csv", data_schema=StockKlineDay)
        
        for stock in stocks:
            kline_count = kline_session.query(StockKlineDay).filter(
                StockKlineDay.entity_id == stock.id
            ).count()
            
            if kline_count > 0:
                # 获取最早和最晚的数据
                earliest = kline_session.query(StockKlineDay).filter(
                    StockKlineDay.entity_id == stock.id
                ).order_by(StockKlineDay.timestamp.asc()).first()
                
                latest = kline_session.query(StockKlineDay).filter(
                    StockKlineDay.entity_id == stock.id
                ).order_by(StockKlineDay.timestamp.desc()).first()
                
                logger.info(f"  {stock.code} K线数据: {kline_count}条")
                logger.info(f"    时间范围: {earliest.timestamp.date()} ~ {latest.timestamp.date()}")
                logger.info(f"    最新收盘价: {latest.close}")
        
        kline_session.close()
        
    except Exception as e:
        logger.error(f"❌ 查询数据失败: {e}")
        raise


def main():
    """主函数"""
    try:
        logger.info("🎯 开始CSV股票数据导入")
        
        # 初始化配置
        init_config()
        init_logging()
        
        # 设置数据库
        setup_database()
        
        # 定义要导入的CSV文件
        csv_files = [
            "sz002436.csv",  # 兴森科技
            "sz002435.csv",  # 长江润发
        ]
        
        # 检查文件是否存在
        for csv_file in csv_files:
            if not Path(csv_file).exists():
                logger.error(f"❌ 文件不存在: {csv_file}")
                return
        
        # 导入数据
        results = import_csv_files(csv_files)
        
        # 查询导入结果
        query_imported_data()
        
        logger.info("🎉 CSV数据导入完成!")
        
    except Exception as e:
        logger.error(f"❌ 程序执行失败: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    main()
