# -*- coding: utf-8 -*-
"""
数据记录器核心类

从ZVT项目中提取的数据记录器基础类和实现。
"""

import logging
import time
import psutil
from datetime import datetime
from typing import List, Union, Type, Any, Dict, Optional

import pandas as pd
import requests
from sqlalchemy.orm import Session

from .service import OneStateService
from .schema import Mixin, TradableEntity
from ..database.session import get_db_session, get_entities, get_data
from ..utils.time_utils import to_pd_timestamp, to_time_str, now_pd_timestamp
from ..utils.data_utils import fill_domain_from_dict, pd_is_not_null
from ..types.enums import IntervalLevel

logger = logging.getLogger(__name__)


class RecorderMeta(type):
    """记录器元类，自动注册记录器到数据模式"""
    
    def __new__(meta, name, bases, class_dict):
        cls = type.__new__(meta, name, bases, class_dict)
        
        # 注册记录器类到数据模式
        if hasattr(cls, "data_schema") and hasattr(cls, "provider"):
            if cls.data_schema and issubclass(cls.data_schema, Mixin):
                logger.debug(f"Registering recorder {cls.__name__} for {cls.data_schema.__name__}")
                cls.data_schema.register_recorder_cls(cls.provider, cls)
        
        return cls


class Recorder(metaclass=RecorderMeta):
    """
    数据记录器基类
    
    提供数据记录的基础框架，包括状态管理、错误处理、重试机制等。
    """
    
    #: 数据提供者名称
    provider: str = None
    #: 数据模式类
    data_schema: Type[Mixin] = None
    
    #: 原始页面URL
    original_page_url = None
    #: 请求URL
    url = None
    
    def __init__(self,
                 force_update: bool = False,
                 sleeping_time: int = 10,
                 ignore_failed: bool = True,
                 return_unfinished: bool = False) -> None:
        """
        初始化记录器

        :param force_update: 是否强制更新
        :param sleeping_time: 休眠时间（秒）
        :param ignore_failed: 是否忽略失败
        :param return_unfinished: 是否返回未完成的实体ID
        """
        assert self.provider is not None, "provider must be set"
        assert self.data_schema is not None, "data_schema must be set"

        self.force_update = force_update
        self.sleeping_time = sleeping_time
        self.ignore_failed = ignore_failed
        self.return_unfinished = return_unfinished

        # 初始化统计信息
        self.stats = {
            'processed_count': 0,
            'success_count': 0,
            'error_count': 0,
            'skipped_count': 0,
            'total_records': 0,
            'start_time': None,
            'end_time': None,
            'start_memory': None,
            'end_memory': None,
            'peak_memory': None,
            'entities_processed': [],
            'errors': []
        }

        # 初始化HTTP会话
        self.http_session = requests.Session()
        self.http_session.headers.update({
            'User-Agent': 'ZVT Data Recorder/1.0.0'
        })

        # 初始化数据库会话
        self.session = get_db_session(provider=self.provider, data_schema=self.data_schema)

        # 初始化日志
        self.logger = logging.getLogger(f"{self.__class__.__module__}.{self.__class__.__name__}")

        # 初始化性能监控
        self._init_performance_monitoring()

    def _init_performance_monitoring(self):
        """初始化性能监控"""
        try:
            # 获取当前进程
            self.process = psutil.Process()
            self.stats['start_memory'] = self.process.memory_info().rss / 1024 / 1024  # MB
            self.stats['peak_memory'] = self.stats['start_memory']
        except Exception as e:
            self.logger.warning(f"性能监控初始化失败: {e}")
            self.process = None

    def _update_memory_stats(self):
        """更新内存统计"""
        try:
            if self.process:
                current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
                if current_memory > self.stats['peak_memory']:
                    self.stats['peak_memory'] = current_memory
        except Exception as e:
            self.logger.debug(f"更新内存统计失败: {e}")

    def _log_progress(self):
        """输出进度信息"""
        try:
            processed = self.stats['processed_count']
            success = self.stats['success_count']
            error = self.stats['error_count']
            skipped = self.stats['skipped_count']

            if processed > 0:
                success_rate = (success / processed * 100)
                error_rate = (error / processed * 100)
                skip_rate = (skipped / processed * 100)

                elapsed_time = time.time() - self.stats['start_time']
                avg_time = elapsed_time / processed

                self.logger.info(f"进度报告: 已处理 {processed} 个实体")
                self.logger.info(f"  成功: {success} ({success_rate:.1f}%)")
                self.logger.info(f"  失败: {error} ({error_rate:.1f}%)")
                self.logger.info(f"  跳过: {skipped} ({skip_rate:.1f}%)")
                self.logger.info(f"  总记录数: {self.stats['total_records']}")
                self.logger.info(f"  平均处理时间: {avg_time:.2f}秒/实体")

                # 内存使用情况
                if self.process:
                    current_memory = self.process.memory_info().rss / 1024 / 1024
                    self.logger.info(f"  当前内存: {current_memory:.1f}MB, 峰值: {self.stats['peak_memory']:.1f}MB")

        except Exception as e:
            self.logger.error(f"输出进度信息失败: {e}")

    def _log_final_stats(self):
        """输出最终统计信息"""
        try:
            elapsed_time = self.stats['end_time'] - self.stats['start_time']
            stats = self.stats

            self.logger.info("=" * 60)
            self.logger.info(f"记录器运行完成: {self.__class__.__name__}")
            self.logger.info("=" * 60)
            self.logger.info(f"总耗时: {elapsed_time:.2f}秒")
            self.logger.info(f"处理实体数: {stats['processed_count']}")
            self.logger.info(f"成功数: {stats['success_count']}")
            self.logger.info(f"失败数: {stats['error_count']}")
            self.logger.info(f"跳过数: {stats['skipped_count']}")
            self.logger.info(f"总记录数: {stats['total_records']}")

            if stats['processed_count'] > 0:
                success_rate = stats['success_count'] / stats['processed_count'] * 100
                avg_time = elapsed_time / stats['processed_count']
                avg_records = stats['total_records'] / stats['processed_count'] if stats['processed_count'] > 0 else 0

                self.logger.info(f"成功率: {success_rate:.1f}%")
                self.logger.info(f"平均处理时间: {avg_time:.2f}秒/实体")
                self.logger.info(f"平均记录数: {avg_records:.1f}条/实体")

                if elapsed_time > 0:
                    throughput = stats['total_records'] / elapsed_time
                    self.logger.info(f"处理吞吐量: {throughput:.1f}条/秒")

            # 内存使用统计
            if self.process and stats['start_memory'] and stats['end_memory']:
                memory_delta = stats['end_memory'] - stats['start_memory']
                self.logger.info(f"内存使用: 开始 {stats['start_memory']:.1f}MB, "
                               f"结束 {stats['end_memory']:.1f}MB, "
                               f"峰值 {stats['peak_memory']:.1f}MB, "
                               f"变化 {memory_delta:+.1f}MB")

            # 错误统计
            if stats['errors']:
                self.logger.info(f"错误详情 (显示前5个):")
                for i, error in enumerate(stats['errors'][:5]):
                    self.logger.info(f"  {i+1}. {error}")
                if len(stats['errors']) > 5:
                    self.logger.info(f"  ... 还有 {len(stats['errors']) - 5} 个错误")

            self.logger.info("=" * 60)

        except Exception as e:
            self.logger.error(f"输出最终统计信息失败: {e}")

    def _record_error(self, entity_id: str, error: Exception):
        """记录错误信息"""
        try:
            error_msg = f"实体 {entity_id}: {str(error)}"
            self.stats['errors'].append(error_msg)

            # 限制错误记录数量，避免内存过度使用
            if len(self.stats['errors']) > 100:
                self.stats['errors'] = self.stats['errors'][-50:]  # 保留最后50个错误

        except Exception as e:
            self.logger.error(f"记录错误信息失败: {e}")

    def _record_success(self, entity_id: str, record_count: int = 0):
        """记录成功信息"""
        try:
            self.stats['success_count'] += 1
            self.stats['total_records'] += record_count
            self.stats['entities_processed'].append(entity_id)

            # 限制成功记录数量
            if len(self.stats['entities_processed']) > 1000:
                self.stats['entities_processed'] = self.stats['entities_processed'][-500:]

        except Exception as e:
            self.logger.error(f"记录成功信息失败: {e}")

    def run(self) -> List[str]:
        """
        运行记录器（增强版本）

        :return: 未完成的实体ID列表（如果return_unfinished为True）
        """
        # 初始化统计信息
        self.stats['start_time'] = time.time()
        if self.process:
            self.stats['start_memory'] = self.process.memory_info().rss / 1024 / 1024
            self.stats['peak_memory'] = self.stats['start_memory']

        self.logger.info(f"开始运行记录器: {self.__class__.__name__}")
        self.logger.info(f"配置参数: force_update={self.force_update}, "
                        f"sleeping_time={self.sleeping_time}, "
                        f"ignore_failed={self.ignore_failed}")

        unfinished_entities = []

        try:
            # 执行具体的记录器逻辑
            unfinished_ids = self._run_impl()

            # 记录结束时间和内存
            self.stats['end_time'] = time.time()
            if self.process:
                self.stats['end_memory'] = self.process.memory_info().rss / 1024 / 1024

            # 输出最终统计信息
            self._log_final_stats()

            return unfinished_ids if self.return_unfinished else []

        except Exception as e:
            self.stats['end_time'] = time.time()
            if self.process:
                self.stats['end_memory'] = self.process.memory_info().rss / 1024 / 1024

            self.logger.error(f"记录器运行失败: {e}")
            self._record_error("SYSTEM", e)
            self._log_final_stats()

            if not self.ignore_failed:
                raise
            return []
    
    def _run_impl(self) -> List[str]:
        """
        记录器运行的具体实现，子类可以重写此方法

        :return: 未完成的实体ID列表
        """
        # 基础实现，子类应该重写此方法
        self.logger.warning("Base recorder run implementation, should be overridden by subclasses")
        return []

    def process_entities_with_stats(self, entities: List[Any],
                                   process_func: callable,
                                   progress_interval: int = 100) -> List[str]:
        """
        带统计信息的实体处理方法

        :param entities: 要处理的实体列表
        :param process_func: 处理单个实体的函数，应返回记录数量
        :param progress_interval: 进度报告间隔
        :return: 未完成的实体ID列表
        """
        unfinished_entities = []

        total_entities = len(entities)
        self.logger.info(f"开始处理 {total_entities} 个实体")

        for i, entity in enumerate(entities):
            entity_id = getattr(entity, 'id', str(entity))

            try:
                # 更新内存统计
                self._update_memory_stats()

                # 处理实体
                record_count = process_func(entity)

                # 记录成功
                if record_count is not None:
                    self._record_success(entity_id, record_count if isinstance(record_count, int) else 0)
                else:
                    # 如果返回None，认为是跳过
                    self.stats['skipped_count'] += 1

            except Exception as e:
                # 记录错误
                self.stats['error_count'] += 1
                self._record_error(entity_id, e)

                if not self.ignore_failed:
                    unfinished_entities.append(entity_id)

                self.logger.error(f"处理实体 {entity_id} 失败: {e}")

            # 更新处理计数
            self.stats['processed_count'] += 1

            # 定期输出进度
            if (i + 1) % progress_interval == 0 or (i + 1) == total_entities:
                self._log_progress()

            # 休眠
            if self.sleeping_time > 0 and i < total_entities - 1:
                time.sleep(self.sleeping_time)

        return unfinished_entities

    def get_stats_summary(self) -> Dict[str, Any]:
        """
        获取统计信息摘要

        :return: 统计信息字典
        """
        stats = self.stats.copy()

        # 计算派生统计信息
        if stats['processed_count'] > 0:
            stats['success_rate'] = stats['success_count'] / stats['processed_count'] * 100
            stats['error_rate'] = stats['error_count'] / stats['processed_count'] * 100
            stats['skip_rate'] = stats['skipped_count'] / stats['processed_count'] * 100
        else:
            stats['success_rate'] = 0
            stats['error_rate'] = 0
            stats['skip_rate'] = 0

        if stats['start_time'] and stats['end_time']:
            elapsed_time = stats['end_time'] - stats['start_time']
            stats['elapsed_time'] = elapsed_time

            if stats['processed_count'] > 0:
                stats['avg_time_per_entity'] = elapsed_time / stats['processed_count']
            else:
                stats['avg_time_per_entity'] = 0

            if elapsed_time > 0:
                stats['throughput'] = stats['total_records'] / elapsed_time
            else:
                stats['throughput'] = 0

        # 内存统计
        if stats['start_memory'] and stats['end_memory']:
            stats['memory_delta'] = stats['end_memory'] - stats['start_memory']

        # 移除详细的错误和实体列表，避免返回过大的数据
        stats.pop('errors', None)
        stats.pop('entities_processed', None)

        return stats
    
    def sleep(self, seconds: int = None):
        """
        休眠指定时间
        
        :param seconds: 休眠秒数，如果为None则使用默认的sleeping_time
        """
        sleep_time = seconds if seconds is not None else self.sleeping_time
        
        if sleep_time and sleep_time > 0:
            self.logger.info(f"休眠 {sleep_time} 秒")
            time.sleep(sleep_time)
    
    def close(self):
        """关闭记录器，清理资源"""
        if hasattr(self, 'http_session'):
            self.http_session.close()
        
        if hasattr(self, 'session'):
            self.session.close()


class EntityEventRecorder(Recorder):
    """
    实体事件记录器
    
    基于实体列表进行数据记录的记录器基类。
    """
    
    #: 实体提供者名称
    entity_provider: str = None
    #: 实体模式类
    entity_schema: Type[TradableEntity] = None
    
    def __init__(self,
                 force_update: bool = False,
                 sleeping_time: int = 10,
                 exchanges: List[str] = None,
                 entity_id: str = None,
                 entity_ids: List[str] = None,
                 code: str = None,
                 codes: List[str] = None,
                 day_data: bool = False,
                 entity_filters: List = None,
                 ignore_failed: bool = True,
                 return_unfinished: bool = False) -> None:
        """
        初始化实体事件记录器
        
        :param force_update: 是否强制更新
        :param sleeping_time: 休眠时间
        :param exchanges: 交易所列表
        :param entity_id: 单个实体ID
        :param entity_ids: 实体ID列表
        :param code: 单个代码
        :param codes: 代码列表
        :param day_data: 是否为日数据
        :param entity_filters: 实体过滤条件
        :param ignore_failed: 是否忽略失败
        :param return_unfinished: 是否返回未完成的实体ID
        """
        super().__init__(
            force_update=force_update,
            sleeping_time=sleeping_time,
            ignore_failed=ignore_failed,
            return_unfinished=return_unfinished
        )
        
        assert self.entity_provider is not None, "entity_provider must be set"
        assert self.entity_schema is not None, "entity_schema must be set"
        
        self.exchanges = exchanges
        self.day_data = day_data
        self.entity_filters = entity_filters
        
        # 处理实体ID和代码
        if entity_id:
            self.entity_ids = [entity_id]
        else:
            self.entity_ids = entity_ids
        
        if code:
            self.codes = [code]
        else:
            self.codes = codes
        
        # 初始化实体会话
        self.entity_session = get_db_session(
            provider=self.entity_provider,
            data_schema=self.entity_schema
        )
        
        # 初始化实体列表
        self.entities = get_entities(
            session=self.entity_session,
            entity_schema=self.entity_schema,
            exchanges=self.exchanges,
            entity_ids=self.entity_ids,
            codes=self.codes,
            return_type="domain",
            provider=self.entity_provider,
            filters=self.entity_filters,
        )
        
        self.logger.info(f"初始化实体列表，共 {len(self.entities)} 个实体")
    
    def _run_impl(self) -> List[str]:
        """
        实体事件记录器的运行实现
        
        :return: 未完成的实体ID列表
        """
        unfinished_ids = []
        
        for i, entity in enumerate(self.entities):
            try:
                self.logger.info(f"处理实体 {i+1}/{len(self.entities)}: {entity.id}")
                
                # 记录单个实体的数据
                self.record_entity(entity)
                
                # 休眠
                if i < len(self.entities) - 1:  # 最后一个实体不需要休眠
                    self.sleep()
                    
            except Exception as e:
                self.logger.error(f"处理实体 {entity.id} 失败: {e}")
                if self.return_unfinished:
                    unfinished_ids.append(entity.id)
                
                if not self.ignore_failed:
                    raise
        
        return unfinished_ids
    
    def record_entity(self, entity):
        """
        记录单个实体的数据，子类应该实现此方法

        :param entity: 实体对象
        """
        raise NotImplementedError("Subclasses must implement record_entity method")


class TimeSeriesDataRecorder(EntityEventRecorder):
    """
    时间序列数据记录器

    专门用于记录时间序列数据的记录器，支持增量更新和时间范围查询。
    """

    #: 默认数据大小
    default_size = 2000

    def __init__(self,
                 force_update: bool = False,
                 sleeping_time: int = 5,
                 exchanges: List[str] = None,
                 entity_id: str = None,
                 entity_ids: List[str] = None,
                 code: str = None,
                 codes: List[str] = None,
                 day_data: bool = False,
                 entity_filters: List = None,
                 ignore_failed: bool = True,
                 real_time: bool = False,
                 fix_duplicate_way: str = "add",
                 start_timestamp: Union[str, pd.Timestamp] = None,
                 end_timestamp: Union[str, pd.Timestamp] = None,
                 return_unfinished: bool = False) -> None:
        """
        初始化时间序列数据记录器

        :param force_update: 是否强制更新
        :param sleeping_time: 休眠时间
        :param exchanges: 交易所列表
        :param entity_id: 单个实体ID
        :param entity_ids: 实体ID列表
        :param code: 单个代码
        :param codes: 代码列表
        :param day_data: 是否为日数据
        :param entity_filters: 实体过滤条件
        :param ignore_failed: 是否忽略失败
        :param real_time: 是否为实时数据
        :param fix_duplicate_way: 处理重复数据的方式
        :param start_timestamp: 开始时间戳
        :param end_timestamp: 结束时间戳
        :param return_unfinished: 是否返回未完成的实体ID
        """
        self.start_timestamp = to_pd_timestamp(start_timestamp)
        self.end_timestamp = to_pd_timestamp(end_timestamp)
        self.real_time = real_time
        self.fix_duplicate_way = fix_duplicate_way

        super().__init__(
            force_update=force_update,
            sleeping_time=sleeping_time,
            exchanges=exchanges,
            entity_id=entity_id,
            entity_ids=entity_ids,
            code=code,
            codes=codes,
            day_data=day_data,
            entity_filters=entity_filters,
            ignore_failed=ignore_failed,
            return_unfinished=return_unfinished
        )

    def record_entity(self, entity):
        """
        记录单个实体的时间序列数据

        :param entity: 实体对象
        """
        # 获取时间范围和大小
        start, end, size, timestamps = self.get_time_range_and_size(entity)

        if size == 0:
            self.logger.info(f"实体 {entity.id} 无需更新数据")
            return

        self.logger.info(f"开始记录实体 {entity.id} 的数据，时间范围: {start} ~ {end}，预期大小: {size}")

        try:
            # 调用子类实现的record方法获取数据
            data = self.record(entity, start, end, size, timestamps)

            if not data:
                self.logger.warning(f"实体 {entity.id} 未获取到数据")
                return

            # 保存数据
            saved_count = self.save_data(entity, data)
            self.logger.info(f"实体 {entity.id} 成功保存 {saved_count} 条数据")

        except Exception as e:
            self.logger.error(f"记录实体 {entity.id} 数据失败: {e}")
            raise

    def get_time_range_and_size(self, entity):
        """
        获取时间范围和数据大小

        :param entity: 实体对象
        :return: (start, end, size, timestamps)
        """
        # 获取最新保存的记录
        latest_record = self.get_latest_saved_record(entity)

        # 确定开始时间
        if self.force_update or not latest_record:
            start = self.start_timestamp
        else:
            start = latest_record.timestamp
            if self.start_timestamp and start < self.start_timestamp:
                start = self.start_timestamp

        # 确定结束时间
        end = self.end_timestamp

        # 确定数据大小
        size = self.default_size
        if end and latest_record and latest_record.timestamp > end:
            size = 0

        return start, end, size, None

    def get_latest_saved_record(self, entity):
        """
        获取最新保存的记录

        :param entity: 实体对象
        :return: 最新记录或None
        """
        try:
            time_field = self.get_evaluated_time_field()

            records = get_data(
                data_schema=self.data_schema,
                provider=self.provider,
                entity_ids=[entity.id],
                columns=['id', 'entity_id', time_field],
                return_type="domain",
                session=self.session,
                order=getattr(self.data_schema, time_field).desc(),
                limit=1,
                time_field=time_field
            )

            return records[0] if records else None

        except Exception as e:
            self.logger.warning(f"获取最新记录失败: {e}")
            return None

    def save_data(self, entity, data):
        """
        保存数据到数据库

        :param entity: 实体对象
        :param data: 数据列表
        :return: 保存的记录数
        """
        if not data:
            return 0

        saved_count = 0
        data_map = self.get_data_map()

        for item in data:
            try:
                # 生成域对象
                domain_obj = self.data_schema()

                # 设置基础字段
                domain_obj.id = self.generate_domain_id(entity, item)
                domain_obj.entity_id = entity.id

                # 填充数据
                if data_map:
                    fill_domain_from_dict(domain_obj, item, data_map)
                else:
                    # 直接填充
                    for key, value in item.items():
                        if hasattr(domain_obj, key):
                            setattr(domain_obj, key, value)

                # 保存到数据库
                self.session.merge(domain_obj)
                saved_count += 1

            except Exception as e:
                self.logger.error(f"保存数据项失败: {e}, 数据: {item}")
                if not self.ignore_failed:
                    raise

        # 提交事务
        try:
            self.session.commit()
        except Exception as e:
            self.session.rollback()
            self.logger.error(f"提交事务失败: {e}")
            raise

        return saved_count

    def record(self, entity, start, end, size, timestamps):
        """
        记录数据的核心方法，子类必须实现

        :param entity: 实体对象
        :param start: 开始时间
        :param end: 结束时间
        :param size: 数据大小
        :param timestamps: 时间戳列表
        :return: 数据记录列表
        """
        raise NotImplementedError("Subclasses must implement record method")

    def get_data_map(self) -> Dict[str, tuple]:
        """
        获取数据字段映射

        返回格式: {'original_field': ('domain_field', transform_func)}

        :return: 字段映射字典
        """
        return {}

    def get_evaluated_time_field(self) -> str:
        """
        获取用于评估时间范围的时间字段名

        :return: 时间字段名
        """
        return "timestamp"

    def get_original_time_field(self) -> str:
        """
        获取原始数据中的时间字段名

        :return: 原始时间字段名
        """
        return "timestamp"

    def generate_domain_id(self, entity, original_data, time_fmt: str = "%Y-%m-%d") -> str:
        """
        从实体和原始数据生成域ID

        :param entity: 实体对象
        :param original_data: 原始数据
        :param time_fmt: 时间格式
        :return: 域ID
        """
        timestamp = to_time_str(
            original_data[self.get_original_time_field()],
            fmt=time_fmt.replace("%Y", "YYYY").replace("%m", "MM").replace("%d", "DD")
        )
        return f"{entity.id}_{timestamp}"


class FixedCycleDataRecorder(TimeSeriesDataRecorder):
    """
    固定周期数据记录器

    用于记录固定时间周期的数据，如日K线、分钟K线等。
    """

    def __init__(self,
                 force_update: bool = True,
                 sleeping_time: int = 10,
                 exchanges: List[str] = None,
                 entity_id: str = None,
                 entity_ids: List[str] = None,
                 code: str = None,
                 codes: List[str] = None,
                 day_data: bool = False,
                 entity_filters: List = None,
                 ignore_failed: bool = True,
                 real_time: bool = False,
                 fix_duplicate_way: str = "ignore",
                 start_timestamp: Union[str, pd.Timestamp] = None,
                 end_timestamp: Union[str, pd.Timestamp] = None,
                 level: IntervalLevel = IntervalLevel.LEVEL_1DAY,
                 kdata_use_begin_time: bool = False,
                 one_day_trading_minutes: int = 24 * 60,
                 return_unfinished: bool = False) -> None:
        """
        初始化固定周期数据记录器

        :param level: 时间间隔级别
        :param kdata_use_begin_time: K线数据是否使用开始时间
        :param one_day_trading_minutes: 一天的交易分钟数
        """
        self.level = level
        self.kdata_use_begin_time = kdata_use_begin_time
        self.one_day_trading_minutes = one_day_trading_minutes

        super().__init__(
            force_update=force_update,
            sleeping_time=sleeping_time,
            exchanges=exchanges,
            entity_id=entity_id,
            entity_ids=entity_ids,
            code=code,
            codes=codes,
            day_data=day_data,
            entity_filters=entity_filters,
            ignore_failed=ignore_failed,
            real_time=real_time,
            fix_duplicate_way=fix_duplicate_way,
            start_timestamp=start_timestamp,
            end_timestamp=end_timestamp,
            return_unfinished=return_unfinished
        )

    def get_time_range_and_size(self, entity):
        """
        获取固定周期的时间范围和大小

        :param entity: 实体对象
        :return: (start, end, size, timestamps)
        """
        # 生成时间戳序列
        timestamps = self.generate_timestamps(entity)

        if not timestamps:
            return None, None, 0, None

        timestamps.sort()

        self.logger.info(f"实体 {entity.id} 时间戳范围: {timestamps[0]} ~ {timestamps[-1]}")

        # 获取最新记录
        latest_record = self.get_latest_saved_record(entity)

        if latest_record:
            self.logger.info(f"最新记录时间戳: {latest_record.timestamp}")
            # 过滤已有的时间戳
            timestamps = [t for t in timestamps if t >= latest_record.timestamp]

            if timestamps:
                return timestamps[0], timestamps[-1], len(timestamps), timestamps
            return None, None, 0, None

        return timestamps[0], timestamps[-1], len(timestamps), timestamps

    def generate_timestamps(self, entity) -> List[pd.Timestamp]:
        """
        生成时间戳序列

        :param entity: 实体对象
        :return: 时间戳列表
        """
        # 基础实现，子类可以重写
        timestamps = []

        start = self.start_timestamp or (now_pd_timestamp() - pd.Timedelta(days=30))
        end = self.end_timestamp or now_pd_timestamp()

        current = start
        while current <= end:
            timestamps.append(current)

            # 根据级别增加时间
            if self.level == IntervalLevel.LEVEL_1DAY:
                current += pd.Timedelta(days=1)
            elif self.level == IntervalLevel.LEVEL_1HOUR:
                current += pd.Timedelta(hours=1)
            elif self.level == IntervalLevel.LEVEL_5MIN:
                current += pd.Timedelta(minutes=5)
            else:
                current += pd.Timedelta(days=1)  # 默认日级别

        return timestamps


__all__ = [
    "RecorderMeta",
    "Recorder",
    "EntityEventRecorder",
    "TimeSeriesDataRecorder",
    "FixedCycleDataRecorder",
]
