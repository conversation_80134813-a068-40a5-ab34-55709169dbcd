# -*- coding: utf-8 -*-
"""
配置管理

数据记录器的配置管理模块。
"""

import os
import logging
from typing import Dict, Any, Optional
from pathlib import Path


class DataRecorderConfig:
    """
    数据记录器配置类
    """
    
    def __init__(self):
        # 默认配置
        self._config = {
            # 数据路径
            'DATA_PATH': self._get_default_data_path(),
            
            # 数据库配置
            'DB_ENGINE_TYPE': 'sqlite',
            'DB_ECHO': False,
            'DB_POOL_SIZE': 5,
            'DB_MAX_OVERFLOW': 10,
            'DB_POOL_TIMEOUT': 30,
            'DB_POOL_RECYCLE': 3600,
            
            # 日志配置
            'LOG_LEVEL': 'INFO',
            'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'LOG_FILE': None,
            
            # 记录器配置
            'DEFAULT_SLEEPING_TIME': 10,
            'DEFAULT_RETRY_TIMES': 3,
            'DEFAULT_TIMEOUT': 30,
            
            # 邮件通知配置
            'EMAIL_ENABLED': False,
            'EMAIL_SMTP_SERVER': None,
            'EMAIL_SMTP_PORT': 587,
            'EMAIL_USERNAME': None,
            'EMAIL_PASSWORD': None,
            'EMAIL_FROM': None,
            'EMAIL_TO': None,
            
            # 其他配置
            'TIMEZONE': 'Asia/Shanghai',
            'DATE_FORMAT': '%Y-%m-%d',
            'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',
        }
        
        # 从环境变量加载配置
        self._load_from_env()
    
    def _get_default_data_path(self) -> str:
        """获取默认数据路径"""
        data_dir = Path('D:\\MyData\\zvt-data')
        data_dir.mkdir(parents=True, exist_ok=True)
        return str(data_dir)
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_mapping = {
            'ZVT_DATA_PATH': 'DATA_PATH',
            'ZVT_DB_ENGINE_TYPE': 'DB_ENGINE_TYPE',
            'ZVT_DB_ECHO': 'DB_ECHO',
            'ZVT_LOG_LEVEL': 'LOG_LEVEL',
            'ZVT_LOG_FILE': 'LOG_FILE',
            'ZVT_EMAIL_ENABLED': 'EMAIL_ENABLED',
            'ZVT_EMAIL_SMTP_SERVER': 'EMAIL_SMTP_SERVER',
            'ZVT_EMAIL_SMTP_PORT': 'EMAIL_SMTP_PORT',
            'ZVT_EMAIL_USERNAME': 'EMAIL_USERNAME',
            'ZVT_EMAIL_PASSWORD': 'EMAIL_PASSWORD',
            'ZVT_EMAIL_FROM': 'EMAIL_FROM',
            'ZVT_EMAIL_TO': 'EMAIL_TO',
        }
        
        for env_key, config_key in env_mapping.items():
            env_value = os.getenv(env_key)
            if env_value is not None:
                # 类型转换
                if config_key in ['DB_ECHO', 'EMAIL_ENABLED']:
                    env_value = env_value.lower() in ('true', '1', 'yes', 'on')
                elif config_key in ['EMAIL_SMTP_PORT', 'DB_POOL_SIZE', 'DB_MAX_OVERFLOW', 
                                   'DB_POOL_TIMEOUT', 'DB_POOL_RECYCLE']:
                    env_value = int(env_value)
                
                self._config[config_key] = env_value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        :param key: 配置键
        :param default: 默认值
        :return: 配置值
        """
        return self._config.get(key, default)
    
    def set(self, key: str, value: Any):
        """
        设置配置值
        
        :param key: 配置键
        :param value: 配置值
        """
        self._config[key] = value
    
    def update(self, config_dict: Dict[str, Any]):
        """
        批量更新配置
        
        :param config_dict: 配置字典
        """
        self._config.update(config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        :return: 配置字典
        """
        return self._config.copy()
    
    @property
    def DATA_PATH(self) -> str:
        """数据路径"""
        return self._config['DATA_PATH']
    
    @property
    def DB_ENGINE_TYPE(self) -> str:
        """数据库引擎类型"""
        return self._config['DB_ENGINE_TYPE']
    
    @property
    def DB_ECHO(self) -> bool:
        """是否打印SQL语句"""
        return self._config['DB_ECHO']
    
    @property
    def LOG_LEVEL(self) -> str:
        """日志级别"""
        return self._config['LOG_LEVEL']
    
    @property
    def LOG_FORMAT(self) -> str:
        """日志格式"""
        return self._config['LOG_FORMAT']
    
    @property
    def LOG_FILE(self) -> Optional[str]:
        """日志文件路径"""
        return self._config['LOG_FILE']
    
    @property
    def EMAIL_ENABLED(self) -> bool:
        """是否启用邮件通知"""
        return self._config['EMAIL_ENABLED']
    
    @property
    def TIMEZONE(self) -> str:
        """时区"""
        return self._config['TIMEZONE']


# 全局配置实例
_global_config = None


def get_config() -> DataRecorderConfig:
    """
    获取全局配置实例
    
    :return: 配置实例
    """
    global _global_config
    if _global_config is None:
        _global_config = DataRecorderConfig()
    return _global_config


def init_config(config_dict: Dict[str, Any] = None, config_file: str = None):
    """
    初始化配置
    
    :param config_dict: 配置字典
    :param config_file: 配置文件路径
    """
    global _global_config
    _global_config = DataRecorderConfig()
    
    # 从文件加载配置
    if config_file and os.path.exists(config_file):
        import json
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                _global_config.update(file_config)
        except Exception as e:
            logging.warning(f"Failed to load config file {config_file}: {e}")
    
    # 从字典更新配置
    if config_dict:
        _global_config.update(config_dict)


def init_logging(config: DataRecorderConfig = None):
    """
    初始化日志配置
    
    :param config: 配置实例
    """
    if config is None:
        config = get_config()
    
    # 设置日志级别
    level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
    
    # 配置日志格式
    formatter = logging.Formatter(config.LOG_FORMAT)
    
    # 获取根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 添加文件处理器
    if config.LOG_FILE:
        try:
            # 确保日志目录存在
            log_dir = os.path.dirname(config.LOG_FILE)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)
            
            file_handler = logging.FileHandler(config.LOG_FILE, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        except Exception as e:
            logging.warning(f"Failed to create log file handler: {e}")


def create_data_dir(data_path: str = None) -> str:
    """
    创建数据目录
    
    :param data_path: 数据路径
    :return: 创建的数据路径
    """
    if data_path is None:
        config = get_config()
        data_path = config.DATA_PATH
    
    os.makedirs(data_path, exist_ok=True)
    return data_path


__all__ = [
    "DataRecorderConfig",
    "get_config",
    "init_config", 
    "init_logging",
    "create_data_dir",
    
    # 常量
    "DATA_PATH",
    "DB_ENGINE_TYPE",
]

# 导出常量
DATA_PATH = get_config().DATA_PATH
DB_ENGINE_TYPE = get_config().DB_ENGINE_TYPE
