#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ZVT Data Recorder 高级用法示例

这个示例展示了更高级的功能，包括：
1. 自定义状态管理
2. 批量数据记录
3. 数据验证和清洗
4. 邮件通知
5. 多数据源支持
"""

import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

import pandas as pd
import requests
from sqlalchemy import Column, String, Float, DateTime, Integer, Text
from sqlalchemy.orm import declarative_base

from zvt_data_recorder import (
    Mixin, TradableEntity, TimeSeriesDataRecorder,
    OneStateService, get_db_session
)
from zvt_data_recorder.database import register_schema
from zvt_data_recorder.config import init_config, init_logging
from zvt_data_recorder.utils import batch_record_data, estimate_recording_time
from zvt_data_recorder.notifier import EmailInformer

# 初始化日志
init_logging()
logger = logging.getLogger(__name__)

# 创建数据库基类
Base = declarative_base()


# 1. 定义状态模型
class RecorderState(Base, Mixin):
    """记录器状态模型"""
    __tablename__ = 'recorder_state'
    
    state_name = Column(String(128))  # 服务名称
    state = Column(Text())  # JSON状态


# 2. 定义加密货币实体
class Cryptocurrency(Base, TradableEntity):
    """加密货币实体"""
    __tablename__ = 'cryptocurrency'
    
    symbol = Column(String(20))  # 交易符号，如 BTC, ETH
    base_currency = Column(String(10))  # 基础货币
    quote_currency = Column(String(10))  # 计价货币
    market_cap_rank = Column(Integer)  # 市值排名


# 3. 定义价格数据模型
class CryptoPrice(Base, Mixin):
    """加密货币价格数据"""
    __tablename__ = 'crypto_price'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    market_cap = Column(Float)
    
    # 技术指标
    rsi = Column(Float)  # RSI指标
    macd = Column(Float)  # MACD指标


# 4. 定义新闻数据模型
class CryptoNews(Base, Mixin):
    """加密货币新闻数据"""
    __tablename__ = 'crypto_news'
    
    title = Column(String(500))
    content = Column(Text())
    source = Column(String(100))
    sentiment_score = Column(Float)  # 情感分析得分


# 5. 自定义状态管理服务
class CryptoRecorderService(OneStateService):
    """加密货币记录器状态管理服务"""
    
    state_schema = RecorderState
    
    def __init__(self):
        super().__init__()
        if self.state is None:
            self.state = {
                'last_update': None,
                'total_records': 0,
                'failed_entities': [],
                'success_rate': 0.0
            }
    
    def update_success(self, entity_id: str, record_count: int):
        """更新成功记录"""
        self.state['last_update'] = datetime.now().isoformat()
        self.state['total_records'] += record_count
        
        # 从失败列表中移除
        if entity_id in self.state['failed_entities']:
            self.state['failed_entities'].remove(entity_id)
        
        self.persist_state()
    
    def update_failure(self, entity_id: str, error: str):
        """更新失败记录"""
        if entity_id not in self.state['failed_entities']:
            self.state['failed_entities'].append(entity_id)
        
        self.persist_state()
        logger.error(f"Failed to record {entity_id}: {error}")


# 6. 高级价格数据记录器
class AdvancedCryptoPriceRecorder(TimeSeriesDataRecorder):
    """高级加密货币价格记录器"""
    
    provider = "coingecko"
    data_schema = CryptoPrice
    entity_provider = "coingecko"
    entity_schema = Cryptocurrency
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.service = CryptoRecorderService()
        self.api_base_url = "https://api.coingecko.com/api/v3"
    
    def record(self, entity, start, end, size, timestamps):
        """记录加密货币价格数据"""
        try:
            # 模拟API调用（实际使用时替换为真实API）
            records = self._fetch_price_data(entity, start, end, size)
            
            # 数据验证和清洗
            cleaned_records = self._clean_data(records)
            
            # 计算技术指标
            enhanced_records = self._calculate_indicators(cleaned_records)
            
            # 更新状态
            self.service.update_success(entity.id, len(enhanced_records))
            
            return enhanced_records
            
        except Exception as e:
            self.service.update_failure(entity.id, str(e))
            raise
    
    def _fetch_price_data(self, entity, start, end, size):
        """获取价格数据（模拟）"""
        # 这里应该是真实的API调用
        # 为了示例，我们生成模拟数据
        records = []
        current_date = start if start else datetime.now() - timedelta(days=30)
        end_date = end if end else datetime.now()
        
        base_price = 50000.0  # 假设是BTC价格
        
        while current_date <= end_date and len(records) < (size or 30):
            import random
            
            # 生成价格数据
            change = random.uniform(-0.1, 0.1)
            base_price *= (1 + change)
            
            record = {
                'timestamp': current_date,
                'open': base_price * random.uniform(0.98, 1.02),
                'high': base_price * random.uniform(1.0, 1.08),
                'low': base_price * random.uniform(0.92, 1.0),
                'close': base_price,
                'volume': random.uniform(1000000, 10000000),
                'market_cap': base_price * 19000000,  # 假设总供应量
            }
            
            records.append(record)
            current_date += timedelta(hours=1)  # 小时级数据
        
        return records
    
    def _clean_data(self, records):
        """数据清洗"""
        cleaned = []
        
        for record in records:
            # 检查数据完整性
            if all(key in record for key in ['open', 'high', 'low', 'close', 'volume']):
                # 检查价格逻辑
                if record['low'] <= record['open'] <= record['high'] and \
                   record['low'] <= record['close'] <= record['high']:
                    # 四舍五入
                    for key in ['open', 'high', 'low', 'close', 'market_cap']:
                        if key in record:
                            record[key] = round(record[key], 2)
                    
                    record['volume'] = int(record['volume'])
                    cleaned.append(record)
                else:
                    logger.warning(f"Invalid price data: {record}")
            else:
                logger.warning(f"Incomplete data: {record}")
        
        return cleaned
    
    def _calculate_indicators(self, records):
        """计算技术指标"""
        if len(records) < 14:  # RSI需要至少14个数据点
            return records
        
        # 转换为DataFrame进行计算
        df = pd.DataFrame(records)
        
        # 计算RSI
        df['rsi'] = self._calculate_rsi(df['close'])
        
        # 计算MACD
        df['macd'] = self._calculate_macd(df['close'])
        
        # 转换回字典列表
        return df.to_dict('records')
    
    def _calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.round(2)
    
    def _calculate_macd(self, prices, fast=12, slow=26):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        return macd.round(2)


# 7. 新闻数据记录器
class CryptoNewsRecorder(TimeSeriesDataRecorder):
    """加密货币新闻记录器"""
    
    provider = "newsapi"
    data_schema = CryptoNews
    entity_provider = "coingecko"
    entity_schema = Cryptocurrency
    
    def record(self, entity, start, end, size, timestamps):
        """记录新闻数据"""
        # 模拟新闻数据
        news_data = [
            {
                'timestamp': datetime.now() - timedelta(hours=i),
                'title': f'{entity.name} 相关新闻标题 {i}',
                'content': f'这是关于 {entity.name} 的新闻内容...',
                'source': 'CryptoNews',
                'sentiment_score': round(random.uniform(-1, 1), 2)
            }
            for i in range(min(size or 10, 10))
        ]
        
        return news_data


def setup_crypto_data():
    """设置加密货币数据"""
    logger.info("Setting up cryptocurrency data...")
    
    # 注册数据模式
    register_schema(
        providers=["coingecko", "newsapi"],
        db_name="crypto_data",
        schema_base=Base,
        entity_type="cryptocurrency"
    )
    
    # 注册提供者
    for schema_cls in [Cryptocurrency, CryptoPrice, CryptoNews, RecorderState]:
        schema_cls.register_provider("coingecko")
        if schema_cls != RecorderState:
            schema_cls.register_provider("newsapi")
    
    # 注册记录器
    CryptoPrice.register_recorder_cls("coingecko", AdvancedCryptoPriceRecorder)
    CryptoNews.register_recorder_cls("newsapi", CryptoNewsRecorder)
    
    # 创建表
    session = get_db_session(provider="coingecko", data_schema=Cryptocurrency)
    Base.metadata.create_all(session.bind)
    
    # 创建示例加密货币
    cryptos = [
        {
            'id': 'crypto_btc',
            'entity_id': 'crypto_btc',
            'code': 'BTC',
            'name': 'Bitcoin',
            'symbol': 'BTC',
            'base_currency': 'BTC',
            'quote_currency': 'USD',
            'market_cap_rank': 1
        },
        {
            'id': 'crypto_eth',
            'entity_id': 'crypto_eth',
            'code': 'ETH',
            'name': 'Ethereum',
            'symbol': 'ETH',
            'base_currency': 'ETH',
            'quote_currency': 'USD',
            'market_cap_rank': 2
        }
    ]
    
    for crypto_data in cryptos:
        existing = session.query(Cryptocurrency).filter(
            Cryptocurrency.id == crypto_data['id']
        ).first()
        if not existing:
            crypto = Cryptocurrency(**crypto_data)
            session.add(crypto)
    
    session.commit()
    session.close()
    
    return [crypto['entity_id'] for crypto in cryptos]


def main():
    """主函数"""
    print("🚀 ZVT Data Recorder 高级用法示例")
    print("=" * 50)
    
    # 1. 初始化配置
    print("\n📋 1. 初始化配置...")
    config = {
        'DATA_PATH': './data',
        'LOG_LEVEL': 'INFO',
        'EMAIL_ENABLED': False,  # 可以设置为True并配置邮件参数
        'EMAIL_SMTP_SERVER': 'smtp.gmail.com',
        'EMAIL_SMTP_PORT': 587,
        # 'EMAIL_USERNAME': '<EMAIL>',
        # 'EMAIL_PASSWORD': 'your-password',
    }
    init_config(config)
    
    # 2. 设置数据
    print("\n📊 2. 设置加密货币数据...")
    entity_ids = setup_crypto_data()
    
    # 3. 估算记录时间
    print("\n⏱️ 3. 估算记录时间...")
    time_estimate = estimate_recording_time(
        domain=CryptoPrice,
        entity_count=len(entity_ids),
        sleeping_time=2
    )
    print(f"预计记录时间: {time_estimate['estimated_duration']}")
    
    # 4. 批量记录数据
    print("\n📈 4. 批量记录数据...")
    
    domains = [CryptoPrice, CryptoNews]
    results = batch_record_data(
        domains=domains,
        provider="coingecko",
        entity_ids=entity_ids,
        sleeping_time=1,
        force_update=True
    )
    
    print(f"\n批量记录结果:")
    print(f"- 成功: {results['success_count']}")
    print(f"- 失败: {results['failed_count']}")
    print(f"- 总耗时: {results['duration']:.2f}秒")
    
    # 5. 查询数据
    print("\n🔍 5. 查询记录的数据...")
    
    try:
        # 查询价格数据
        prices_df = CryptoPrice.query_data(
            provider="coingecko",
            entity_ids=entity_ids,
            limit=5
        )
        print(f"\n价格数据 ({len(prices_df)} 条记录):")
        if not prices_df.empty:
            print(prices_df[['entity_id', 'timestamp', 'close', 'volume', 'rsi', 'macd']].to_string())
        
        # 查询新闻数据
        news_df = CryptoNews.query_data(
            provider="newsapi",
            entity_ids=entity_ids,
            limit=3
        )
        print(f"\n新闻数据 ({len(news_df)} 条记录):")
        if not news_df.empty:
            print(news_df[['entity_id', 'title', 'sentiment_score']].to_string())
        
    except Exception as e:
        logger.error(f"数据查询失败: {e}")
    
    # 6. 状态管理示例
    print("\n📊 6. 查看记录器状态...")
    service = CryptoRecorderService()
    if service.state:
        print(f"状态信息:")
        print(f"- 最后更新: {service.state.get('last_update')}")
        print(f"- 总记录数: {service.state.get('total_records')}")
        print(f"- 失败实体: {service.state.get('failed_entities')}")
    
    # 7. 邮件通知示例（如果启用）
    if config.get('EMAIL_ENABLED'):
        print("\n📧 7. 发送邮件通知...")
        email_informer = EmailInformer()
        
        if email_informer.test_connection():
            email_informer.send_data_report(
                report_title="加密货币数据记录报告",
                data_summary={
                    "记录实体数": len(entity_ids),
                    "成功记录": results['success_count'],
                    "失败记录": results['failed_count'],
                    "总耗时": f"{results['duration']:.2f}秒"
                }
            )
            print("✅ 邮件通知发送成功")
        else:
            print("❌ 邮件服务器连接失败")
    
    print("\n🎉 高级示例运行完成！")


if __name__ == "__main__":
    import random
    main()
