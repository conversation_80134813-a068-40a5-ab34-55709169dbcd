#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础功能测试

测试ZVT Data Recorder的基础功能是否正常工作。
"""

import os
import sys
import tempfile
import unittest
from datetime import datetime, timedelta

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import pandas as pd
from sqlalchemy import Column, String, Float, DateTime, Integer
from sqlalchemy.orm import declarative_base

from zvt_data_recorder.core.schema import Mixin
from zvt_data_recorder.core.recorder import TimeSeriesDataRecorder
from zvt_data_recorder.database.session import get_db_session
from zvt_data_recorder.types.enums import IntervalLevel

# 创建测试用的数据库基类
Base = declarative_base()


class TestStock(Base, Mixin):
    """测试股票实体"""
    __tablename__ = 'test_stock'

    code = Column(String(20))
    name = Column(String(100))
    market = Column(String(10))
    industry = Column(String(100))


class TestStockPrice(Base, Mixin):
    """测试股票价格数据"""
    __tablename__ = 'test_stock_price'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Integer)


class TestStockPriceRecorder(TimeSeriesDataRecorder):
    """测试股票价格记录器"""
    
    provider = "test"
    data_schema = TestStockPrice
    entity_provider = "test"
    entity_schema = TestStock
    
    def record(self, entity, start, end, size, timestamps):
        """生成测试数据"""
        records = []
        current_date = start if start else datetime.now() - timedelta(days=10)
        end_date = end if end else datetime.now()
        
        base_price = 10.0
        
        while current_date <= end_date and len(records) < (size or 10):
            import random
            
            change_pct = random.uniform(-0.02, 0.02)
            base_price *= (1 + change_pct)
            
            record = {
                'timestamp': current_date,
                'open': round(base_price * 0.99, 2),
                'high': round(base_price * 1.02, 2),
                'low': round(base_price * 0.98, 2),
                'close': round(base_price, 2),
                'volume': random.randint(100000, 1000000),
            }
            
            records.append(record)
            current_date += timedelta(days=1)
        
        return records


class TestBasicFunctionality(unittest.TestCase):
    """基础功能测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 创建临时目录作为数据路径
        self.temp_dir = tempfile.mkdtemp()
        
        # 初始化配置
        config = {
            'DATA_PATH': self.temp_dir,
            'LOG_LEVEL': 'DEBUG',
            'EMAIL_ENABLED': False,
        }
        init_config(config)
        
        # 注册数据模式
        register_schema(
            providers=["test"],
            db_name="test_data",
            schema_base=Base,
            entity_type="test_stock"
        )
        
        # 注册提供者和记录器
        TestStock.register_provider("test")
        TestStockPrice.register_provider("test")
        TestStockPrice.register_recorder_cls("test", TestStockPriceRecorder)
        
        # 创建测试数据库表
        session = get_db_session(provider="test", data_schema=TestStock)
        Base.metadata.create_all(session.bind)
        session.close()
        
        # 创建测试股票
        self.create_test_stocks()
    
    def tearDown(self):
        """测试后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def create_test_stocks(self):
        """创建测试股票数据"""
        session = get_db_session(provider="test", data_schema=TestStock)
        
        stocks = [
            {
                'id': 'test_stock_001',
                'entity_id': 'test_stock_001',
                'code': '000001',
                'name': '测试股票1',
                'market': 'SZ',
                'industry': '测试行业',
                'list_date': datetime(2020, 1, 1),
            },
            {
                'id': 'test_stock_002',
                'entity_id': 'test_stock_002',
                'code': '000002',
                'name': '测试股票2',
                'market': 'SZ',
                'industry': '测试行业',
                'list_date': datetime(2020, 1, 1),
            }
        ]
        
        for stock_data in stocks:
            stock = TestStock(**stock_data)
            session.add(stock)
        
        session.commit()
        session.close()
    
    def test_database_connection(self):
        """测试数据库连接"""
        session = get_db_session(provider="test", data_schema=TestStock)
        self.assertIsNotNone(session)
        
        # 测试查询
        stocks = session.query(TestStock).all()
        self.assertEqual(len(stocks), 2)
        
        session.close()
    
    def test_entity_query(self):
        """测试实体查询"""
        # 测试DataFrame格式查询
        df = TestStock.query_data(provider="test", return_type="df")
        self.assertFalse(df.empty)
        self.assertEqual(len(df), 2)
        self.assertIn('code', df.columns)
        self.assertIn('name', df.columns)
        
        # 测试字典格式查询
        data = TestStock.query_data(provider="test", return_type="dict")
        self.assertEqual(len(data), 2)
        self.assertIsInstance(data[0], dict)
        
        # 测试域对象格式查询
        entities = TestStock.query_data(provider="test", return_type="domain")
        self.assertEqual(len(entities), 2)
        self.assertIsInstance(entities[0], TestStock)
    
    def test_recorder_creation(self):
        """测试记录器创建"""
        recorder = TestStockPriceRecorder(
            entity_ids=['test_stock_001'],
            sleeping_time=0,  # 测试时不休眠
            force_update=True
        )
        
        self.assertIsNotNone(recorder)
        self.assertEqual(recorder.provider, "test")
        self.assertEqual(recorder.data_schema, TestStockPrice)
        self.assertEqual(len(recorder.entities), 1)
    
    def test_data_recording(self):
        """测试数据记录"""
        # 创建记录器
        recorder = TestStockPriceRecorder(
            entity_ids=['test_stock_001'],
            sleeping_time=0,
            force_update=True
        )
        
        # 运行记录器
        unfinished = recorder.run()
        self.assertEqual(len(unfinished), 0)
        
        # 验证数据是否保存
        df = TestStockPrice.query_data(
            provider="test",
            entity_ids=['test_stock_001'],
            return_type="df"
        )
        
        self.assertFalse(df.empty)
        self.assertGreater(len(df), 0)
        self.assertIn('open', df.columns)
        self.assertIn('close', df.columns)
        self.assertIn('volume', df.columns)
    
    def test_incremental_update(self):
        """测试增量更新"""
        entity_id = 'test_stock_002'
        
        # 第一次记录
        recorder1 = TestStockPriceRecorder(
            entity_ids=[entity_id],
            sleeping_time=0,
            force_update=True,
            start_timestamp=datetime.now() - timedelta(days=5),
            end_timestamp=datetime.now() - timedelta(days=3)
        )
        recorder1.run()
        
        # 查询第一次记录的数据
        df1 = TestStockPrice.query_data(
            provider="test",
            entity_ids=[entity_id],
            return_type="df"
        )
        count1 = len(df1)
        
        # 第二次记录（增量）
        recorder2 = TestStockPriceRecorder(
            entity_ids=[entity_id],
            sleeping_time=0,
            force_update=False,  # 不强制更新，应该增量更新
            start_timestamp=datetime.now() - timedelta(days=2),
            end_timestamp=datetime.now()
        )
        recorder2.run()
        
        # 查询第二次记录后的数据
        df2 = TestStockPrice.query_data(
            provider="test",
            entity_ids=[entity_id],
            return_type="df"
        )
        count2 = len(df2)
        
        # 验证数据增加了
        self.assertGreater(count2, count1)
    
    def test_utility_function(self):
        """测试工具函数"""
        # 测试run_data_recorder函数
        unfinished = run_data_recorder(
            domain=TestStockPrice,
            data_provider="test",
            entity_provider="test",
            entity_ids=['test_stock_001'],
            sleeping_time=0,
            force_update=True,
            return_unfinished=True
        )
        
        self.assertEqual(len(unfinished), 0)
        
        # 验证数据
        df = TestStockPrice.query_data(
            provider="test",
            entity_ids=['test_stock_001'],
            return_type="df"
        )
        self.assertFalse(df.empty)
    
    def test_data_validation(self):
        """测试数据验证"""
        # 记录数据
        recorder = TestStockPriceRecorder(
            entity_ids=['test_stock_001'],
            sleeping_time=0,
            force_update=True
        )
        recorder.run()
        
        # 查询数据并验证
        df = TestStockPrice.query_data(
            provider="test",
            entity_ids=['test_stock_001'],
            return_type="df"
        )
        
        # 验证数据完整性
        self.assertFalse(df.empty)
        
        # 验证必要字段存在
        required_columns = ['entity_id', 'timestamp', 'open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            self.assertIn(col, df.columns)
        
        # 验证数据类型
        self.assertTrue(pd.api.types.is_numeric_dtype(df['open']))
        self.assertTrue(pd.api.types.is_numeric_dtype(df['close']))
        self.assertTrue(pd.api.types.is_integer_dtype(df['volume']))
        
        # 验证价格逻辑（high >= low）
        self.assertTrue((df['high'] >= df['low']).all())


if __name__ == '__main__':
    unittest.main()
