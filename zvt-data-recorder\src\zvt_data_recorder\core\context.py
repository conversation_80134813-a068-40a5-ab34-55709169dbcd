# -*- coding: utf-8 -*-
"""
上下文管理

从ZVT项目中提取的上下文管理功能，用于管理全局状态和配置。
"""

from typing import Dict, List, Type


class DataRecorderContext(object):
    """
    数据记录器上下文管理类
    
    管理全局的数据提供者、数据模式、数据库连接等信息。
    """

    def __init__(self) -> None:
        #: 所有注册的数据提供者
        self.providers: List[str] = []

        #: 所有注册的实体类型(字符串)
        self.tradable_entity_types: List[str] = []

        #: 所有实体模式
        self.tradable_entity_schemas: List = []

        #: 所有注册的模式
        self.schemas: List = []

        #: 可交易实体类型 -> 模式映射
        self.tradable_schema_map: Dict[str, Type] = {}

        #: 全局会话
        self.sessions: Dict[str, object] = {}

        #: provider_dbname -> 数据库引擎
        self.db_engine_map: Dict[str, object] = {}

        #: provider_dbname -> 会话工厂
        self.db_session_map: Dict[str, object] = {}

        #: provider -> [db_name1, db_name2, ...]
        self.provider_map_dbnames: Dict[str, List[str]] = {}

        #: db_name -> [declarative_base1, declarative_base2, ...]
        self.dbname_map_base: Dict[str, List] = {}

        #: db_name -> [declarative_meta1, declarative_meta2, ...]
        self.dbname_map_schemas: Dict[str, List] = {}

        #: entity_type -> 相关模式
        self.entity_map_schemas: Dict[str, List] = {}

    def register_provider(self, provider: str):
        """
        注册数据提供者
        
        :param provider: 提供者名称
        """
        if provider not in self.providers:
            self.providers.append(provider)

    def register_entity_type(self, entity_type: str, schema_cls: Type):
        """
        注册实体类型
        
        :param entity_type: 实体类型
        :param schema_cls: 模式类
        """
        if entity_type not in self.tradable_entity_types:
            self.tradable_entity_types.append(entity_type)
            self.tradable_entity_schemas.append(schema_cls)
        
        self.tradable_schema_map[entity_type] = schema_cls

    def register_schema(self, schema_cls: Type, db_name: str, provider: str):
        """
        注册数据模式
        
        :param schema_cls: 模式类
        :param db_name: 数据库名称
        :param provider: 提供者名称
        """
        if schema_cls not in self.schemas:
            self.schemas.append(schema_cls)

        # 注册提供者
        self.register_provider(provider)

        # 建立provider -> db_name映射
        if provider not in self.provider_map_dbnames:
            self.provider_map_dbnames[provider] = []
        if db_name not in self.provider_map_dbnames[provider]:
            self.provider_map_dbnames[provider].append(db_name)

        # 建立db_name -> schema映射
        if db_name not in self.dbname_map_schemas:
            self.dbname_map_schemas[db_name] = []
        if schema_cls not in self.dbname_map_schemas[db_name]:
            self.dbname_map_schemas[db_name].append(schema_cls)

    def get_providers(self) -> List[str]:
        """获取所有提供者"""
        return self.providers.copy()

    def get_entity_types(self) -> List[str]:
        """获取所有实体类型"""
        return self.tradable_entity_types.copy()

    def get_entity_schema(self, entity_type: str) -> Type:
        """
        根据实体类型获取模式类
        
        :param entity_type: 实体类型
        :return: 模式类
        """
        return self.tradable_schema_map.get(entity_type)

    def get_schemas_by_provider(self, provider: str) -> List[Type]:
        """
        根据提供者获取所有模式
        
        :param provider: 提供者名称
        :return: 模式列表
        """
        schemas = []
        db_names = self.provider_map_dbnames.get(provider, [])
        for db_name in db_names:
            schemas.extend(self.dbname_map_schemas.get(db_name, []))
        return schemas

    def get_db_names_by_provider(self, provider: str) -> List[str]:
        """
        根据提供者获取数据库名称列表
        
        :param provider: 提供者名称
        :return: 数据库名称列表
        """
        return self.provider_map_dbnames.get(provider, []).copy()

    def clear(self):
        """清空所有注册信息"""
        self.providers.clear()
        self.tradable_entity_types.clear()
        self.tradable_entity_schemas.clear()
        self.schemas.clear()
        self.tradable_schema_map.clear()
        self.sessions.clear()
        self.db_engine_map.clear()
        self.db_session_map.clear()
        self.provider_map_dbnames.clear()
        self.dbname_map_base.clear()
        self.dbname_map_schemas.clear()
        self.entity_map_schemas.clear()


# 全局上下文实例
_global_context = DataRecorderContext()


def get_context() -> DataRecorderContext:
    """
    获取全局上下文实例
    
    :return: 上下文实例
    """
    return _global_context


def reset_context():
    """重置全局上下文"""
    global _global_context
    _global_context = DataRecorderContext()


__all__ = [
    "DataRecorderContext",
    "get_context", 
    "reset_context",
]
