# -*- coding: utf-8 -*-
"""
服务基类

从ZVT项目中提取的服务基础类，提供状态管理功能。
"""

import json
from typing import Type, List

from ..database.session import del_data, get_db_session
from ..utils.str_utils import to_snake_str


class StateMixin(object):
    """
    状态混入类
    
    为数据模型提供状态管理功能。
    """
    
    #: 服务的唯一名称，例如 recorder, factor, tag
    state_name = None
    
    #: JSON字符串形式的状态
    state = None


class StatefulService(object):
    """
    有状态服务基类
    
    状态可以存储在state_schema中的服务基类。
    """

    #: 状态模式
    state_schema: Type[StateMixin] = None

    #: 服务名称，如果没有手动设置，默认为类名
    name = None

    def __init__(self) -> None:
        assert self.state_schema is not None
        if self.name is None:
            self.name = to_snake_str(type(self).__name__)
        self.state_session = get_db_session(data_schema=self.state_schema, provider="zvt")

    def clear_state_data(self, entity_id=None):
        """
        清除实体的状态
        
        :param entity_id: 实体ID
        """
        filters = [self.state_schema.state_name == self.name]
        if entity_id:
            filters = filters + [self.state_schema.entity_id == entity_id]
        del_data(self.state_schema, filters=filters)

    def decode_state(self, state: str):
        """
        解码状态
        
        :param state: 状态字符串
        :return: 解码后的状态对象
        """
        return json.loads(state, object_hook=self.state_object_hook())

    def encode_state(self, state: object):
        """
        编码状态
        
        :param state: 状态对象
        :return: 编码后的状态字符串
        """
        return json.dumps(state, cls=self.state_encoder())

    def state_object_hook(self):
        """状态对象钩子函数"""
        return None

    def state_encoder(self):
        """状态编码器"""
        return None


class OneStateService(StatefulService):
    """
    单状态服务
    
    将所有状态保存在一个对象中的有状态服务。
    """

    def __init__(self) -> None:
        super().__init__()
        self.state_domain = self.state_schema.get_by_id(id=self.name)
        if self.state_domain:
            self.state: dict = self.decode_state(self.state_domain.state)
        else:
            self.state = None

    def persist_state(self):
        """持久化状态"""
        state_str = self.encode_state(self.state)
        if not self.state_domain:
            self.state_domain = self.state_schema(id=self.name, entity_id=self.name, state_name=self.name)
        self.state_domain.state = state_str
        self.state_session.add(self.state_domain)
        self.state_session.commit()


class EntityStateService(StatefulService):
    """
    实体状态服务
    
    为每个实体保存一个状态的有状态服务。
    """

    def __init__(self, entity_ids) -> None:
        super().__init__()
        self.entity_ids = entity_ids
        state_domains: List[StateMixin] = self.state_schema.query_data(
            filters=[self.state_schema.state_name == self.name], 
            entity_ids=self.entity_ids, 
            return_type="domain"
        )

        #: entity_id:state
        self.states: dict = {}
        if state_domains:
            for state in state_domains:
                self.states[state.entity_id] = self.decode_state(state.state)

    def persist_state(self, entity_id):
        """
        持久化指定实体的状态
        
        :param entity_id: 实体ID
        """
        state = self.states.get(entity_id)
        if state:
            domain_id = f"{self.name}_{entity_id}"
            state_domain = self.state_schema.get_by_id(domain_id)
            state_str = self.encode_state(state)
            if not state_domain:
                state_domain = self.state_schema(id=domain_id, entity_id=entity_id, state_name=self.name)
            state_domain.state = state_str
            self.state_session.add(state_domain)
            self.state_session.commit()

    def get_state(self, entity_id: str):
        """
        获取指定实体的状态
        
        :param entity_id: 实体ID
        :return: 状态对象
        """
        return self.states.get(entity_id)

    def set_state(self, entity_id: str, state: dict):
        """
        设置指定实体的状态
        
        :param entity_id: 实体ID
        :param state: 状态对象
        """
        self.states[entity_id] = state

    def has_state(self, entity_id: str) -> bool:
        """
        检查是否存在指定实体的状态
        
        :param entity_id: 实体ID
        :return: 是否存在状态
        """
        return entity_id in self.states

    def remove_state(self, entity_id: str):
        """
        移除指定实体的状态
        
        :param entity_id: 实体ID
        """
        if entity_id in self.states:
            del self.states[entity_id]
            
            # 从数据库中删除
            domain_id = f"{self.name}_{entity_id}"
            filters = [self.state_schema.id == domain_id]
            del_data(self.state_schema, filters=filters)

    def persist_all_states(self):
        """持久化所有实体的状态"""
        for entity_id in self.states:
            self.persist_state(entity_id)


class ServiceManager(object):
    """
    服务管理器
    
    管理多个服务实例。
    """
    
    def __init__(self):
        self.services = {}
    
    def register_service(self, name: str, service: StatefulService):
        """
        注册服务
        
        :param name: 服务名称
        :param service: 服务实例
        """
        self.services[name] = service
    
    def get_service(self, name: str) -> StatefulService:
        """
        获取服务
        
        :param name: 服务名称
        :return: 服务实例
        """
        return self.services.get(name)
    
    def remove_service(self, name: str):
        """
        移除服务
        
        :param name: 服务名称
        """
        if name in self.services:
            del self.services[name]
    
    def list_services(self) -> List[str]:
        """
        列出所有服务名称
        
        :return: 服务名称列表
        """
        return list(self.services.keys())
    
    def clear_all_services(self):
        """清除所有服务"""
        self.services.clear()
    
    def get_service_status(self, name: str) -> dict:
        """
        获取服务状态
        
        :param name: 服务名称
        :return: 服务状态信息
        """
        service = self.services.get(name)
        if not service:
            return {'exists': False}
        
        status = {
            'exists': True,
            'name': service.name,
            'class': service.__class__.__name__,
            'state_schema': service.state_schema.__name__ if service.state_schema else None
        }
        
        # 添加特定类型的状态信息
        if isinstance(service, OneStateService):
            status['type'] = 'OneStateService'
            status['has_state'] = service.state is not None
        elif isinstance(service, EntityStateService):
            status['type'] = 'EntityStateService'
            status['entity_count'] = len(service.entity_ids)
            status['state_count'] = len(service.states)
        else:
            status['type'] = 'StatefulService'
        
        return status


# 全局服务管理器
_global_service_manager = ServiceManager()


def get_service_manager() -> ServiceManager:
    """
    获取全局服务管理器
    
    :return: 服务管理器实例
    """
    return _global_service_manager


__all__ = [
    "StateMixin",
    "StatefulService", 
    "OneStateService", 
    "EntityStateService",
    "ServiceManager",
    "get_service_manager",
]
