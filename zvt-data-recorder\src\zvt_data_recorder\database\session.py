# -*- coding: utf-8 -*-
"""
数据库会话管理

从ZVT项目中提取的数据库会话管理功能。
"""

import logging
from typing import List, Union, Type, Any, Dict

import pandas as pd
from sqlalchemy import func, exists, and_, or_
from sqlalchemy.ext.declarative import DeclarativeMeta
from sqlalchemy.orm import Query, sessionmaker, Session
from sqlalchemy.sql.expression import text

from ..core.context import get_context
from ..utils.time_utils import to_pd_timestamp
from ..utils.data_utils import pd_is_not_null, index_df
from .engine import get_db_engine

logger = logging.getLogger(__name__)


def get_db_session(
    provider: str, 
    db_name: str = None, 
    data_schema: Type = None, 
    force_new: bool = False
) -> Session:
    """
    获取数据库会话
    
    :param provider: 数据提供者
    :param db_name: 数据库名称
    :param data_schema: 数据模式
    :param force_new: 是否强制创建新会话
    :return: 数据库会话
    """
    from .engine import _get_db_name
    
    if data_schema:
        db_name = _get_db_name(data_schema=data_schema)
    
    if not db_name:
        raise ValueError("db_name or data_schema must be provided")
    
    session_key = f"{provider}_{db_name}"
    
    if force_new:
        return get_db_session_factory(provider, db_name, data_schema)()
    
    context = get_context()
    session = context.sessions.get(session_key)
    
    # FIXME: 不应该维护全局会话
    if not session:
        session = get_db_session_factory(provider, db_name, data_schema)()
        context.sessions[session_key] = session
    
    return session


def get_db_session_factory(
    provider: str, 
    db_name: str = None, 
    data_schema: Type = None
):
    """
    获取数据库会话工厂
    
    :param provider: 数据提供者
    :param db_name: 数据库名称
    :param data_schema: 数据模式
    :return: 会话工厂
    """
    from .engine import _get_db_name
    
    if data_schema:
        db_name = _get_db_name(data_schema=data_schema)
    
    if not db_name:
        raise ValueError("db_name or data_schema must be provided")
    
    session_key = f"{provider}_{db_name}"
    context = get_context()
    session_factory = context.db_session_map.get(session_key)
    
    if not session_factory:
        engine = get_db_engine(provider=provider, db_name=db_name, data_schema=data_schema)
        session_factory = sessionmaker(bind=engine)
        context.db_session_map[session_key] = session_factory
    
    return session_factory


# 别名
DBSession = get_db_session_factory


def get_schema_columns(schema: DeclarativeMeta) -> List[str]:
    """
    获取模式的列名列表
    
    :param schema: 数据模式
    :return: 列名列表
    """
    return [column.name for column in schema.__table__.columns]


def get_by_id(data_schema: Type, id: str, provider: str) -> Any:
    """
    根据ID获取单个记录
    
    :param data_schema: 数据模式
    :param id: 记录ID
    :param provider: 数据提供者
    :return: 记录对象
    """
    session = get_db_session(provider=provider, data_schema=data_schema)
    try:
        return session.query(data_schema).get(id)
    except Exception as e:
        logger.error(f"Failed to get record by id {id}: {e}")
        return None


def get_count(
    data_schema: Type,
    provider: str,
    filters: List = None,
    session: Session = None
) -> int:
    """
    获取记录数量
    
    :param data_schema: 数据模式
    :param provider: 数据提供者
    :param filters: 过滤条件
    :param session: 数据库会话
    :return: 记录数量
    """
    if session is None:
        session = get_db_session(provider=provider, data_schema=data_schema)
    
    query = session.query(func.count(data_schema.id))
    
    if filters:
        for filter_condition in filters:
            query = query.filter(filter_condition)
    
    try:
        return query.scalar()
    except Exception as e:
        logger.error(f"Failed to get count: {e}")
        return 0


def get_data(
    data_schema: Type,
    provider: str = None,
    ids: List[str] = None,
    entity_ids: List[str] = None,
    codes: List[str] = None,
    level: str = None,
    start_timestamp: Union[str, pd.Timestamp] = None,
    end_timestamp: Union[str, pd.Timestamp] = None,
    columns: List[str] = None,
    return_type: str = "df",
    session: Session = None,
    order = None,
    limit: int = None,
    filters: List = None,
    time_field: str = "timestamp"
) -> Union[pd.DataFrame, List, Dict]:
    """
    查询数据

    :param data_schema: 数据模式
    :param provider: 数据提供者
    :param ids: ID列表
    :param entity_ids: 实体ID列表
    :param codes: 代码列表
    :param level: 级别
    :param start_timestamp: 开始时间
    :param end_timestamp: 结束时间
    :param columns: 列名列表
    :param return_type: 返回类型 ("df", "dict", "domain")
    :param session: 数据库会话
    :param order: 排序
    :param limit: 限制条数
    :param filters: 过滤条件
    :param time_field: 时间字段
    :return: 查询结果
    """
    if session is None:
        session = get_db_session(provider=provider, data_schema=data_schema)

    # 构建查询
    if columns:
        # 确保包含必要的列
        required_cols = ['id', 'entity_id', time_field]
        for col in required_cols:
            if hasattr(data_schema, col) and col not in columns:
                columns.append(col)

        # 构建列查询
        column_attrs = []
        for col in columns:
            if hasattr(data_schema, col):
                column_attrs.append(getattr(data_schema, col))

        if column_attrs:
            query = session.query(*column_attrs)
        else:
            query = session.query(data_schema)
    else:
        query = session.query(data_schema)

    # 添加过滤条件
    if ids:
        query = query.filter(data_schema.id.in_(ids))

    if entity_ids:
        query = query.filter(data_schema.entity_id.in_(entity_ids))

    if codes and hasattr(data_schema, 'code'):
        query = query.filter(data_schema.code.in_(codes))

    if start_timestamp:
        start_ts = to_pd_timestamp(start_timestamp)
        time_attr = getattr(data_schema, time_field)
        query = query.filter(time_attr >= start_ts)

    if end_timestamp:
        end_ts = to_pd_timestamp(end_timestamp)
        time_attr = getattr(data_schema, time_field)
        query = query.filter(time_attr <= end_ts)

    if filters:
        for filter_condition in filters:
            query = query.filter(filter_condition)

    # 添加排序
    if order is not None:
        query = query.order_by(order)

    # 添加限制
    if limit:
        query = query.limit(limit)

    try:
        # 执行查询
        if return_type == "domain":
            return query.all()
        elif return_type == "dict":
            results = query.all()
            if columns:
                # 如果指定了列，需要手动构建字典
                return [dict(zip(columns, row)) for row in results]
            else:
                return [row.__dict__ for row in results]
        else:  # return_type == "df"
            df = pd.read_sql(query.statement, session.bind)
            if pd_is_not_null(df):
                # 设置索引
                if 'entity_id' in df.columns and time_field in df.columns:
                    df = index_df(df, index=['entity_id', time_field], time_field=time_field)
            return df

    except Exception as e:
        logger.error(f"Failed to query data: {e}")
        if return_type == "df":
            return pd.DataFrame()
        else:
            return []


def get_entities(
    session: Session,
    entity_schema: Type,
    exchanges: List[str] = None,
    entity_ids: List[str] = None,
    codes: List[str] = None,
    return_type: str = "domain",
    provider: str = None,
    filters: List = None
) -> Union[List, pd.DataFrame]:
    """
    获取实体列表

    :param session: 数据库会话
    :param entity_schema: 实体模式
    :param exchanges: 交易所列表
    :param entity_ids: 实体ID列表
    :param codes: 代码列表
    :param return_type: 返回类型
    :param provider: 数据提供者
    :param filters: 过滤条件
    :return: 实体列表
    """
    query = session.query(entity_schema)

    # 添加过滤条件
    if entity_ids:
        query = query.filter(entity_schema.entity_id.in_(entity_ids))

    if codes and hasattr(entity_schema, 'code'):
        query = query.filter(entity_schema.code.in_(codes))

    if exchanges and hasattr(entity_schema, 'exchange'):
        query = query.filter(entity_schema.exchange.in_(exchanges))

    if filters:
        for filter_condition in filters:
            query = query.filter(filter_condition)

    try:
        results = query.all()

        if return_type == "df":
            if results:
                data = [result.__dict__ for result in results]
                df = pd.DataFrame(data)
                # 移除SQLAlchemy的内部属性
                df = df.drop(columns=[col for col in df.columns if col.startswith('_')])
                return df
            else:
                return pd.DataFrame()
        else:
            return results

    except Exception as e:
        logger.error(f"Failed to get entities: {e}")
        if return_type == "df":
            return pd.DataFrame()
        else:
            return []


def del_data(
    data_schema: Type,
    provider: str = None,
    filters: List = None,
    session: Session = None
):
    """
    删除数据

    :param data_schema: 数据模式
    :param provider: 数据提供者
    :param filters: 过滤条件
    :param session: 数据库会话
    """
    if session is None:
        session = get_db_session(provider=provider, data_schema=data_schema)

    query = session.query(data_schema)

    if filters:
        for filter_condition in filters:
            query = query.filter(filter_condition)

    try:
        count = query.count()
        query.delete(synchronize_session=False)
        session.commit()
        logger.info(f"Deleted {count} records from {data_schema.__name__}")
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to delete data: {e}")
        raise


__all__ = [
    "get_db_session",
    "get_db_session_factory",
    "DBSession",
    "get_schema_columns",
    "get_by_id",
    "get_count",
    "get_data",
    "get_entities",
    "del_data",
]
