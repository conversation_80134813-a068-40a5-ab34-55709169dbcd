# -*- coding: utf-8 -*-
"""
邮件通知模块

从ZVT项目中提取的邮件通知功能。
"""

import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional

from ..config.settings import get_config

logger = logging.getLogger(__name__)


class EmailInformer(object):
    """
    邮件通知器
    
    提供邮件发送功能，支持多种邮件服务器。
    """
    
    def __init__(self, 
                 smtp_server: str = None,
                 smtp_port: int = None,
                 username: str = None,
                 password: str = None,
                 from_email: str = None,
                 to_emails: List[str] = None):
        """
        初始化邮件通知器
        
        :param smtp_server: SMTP服务器地址
        :param smtp_port: SMTP端口
        :param username: 用户名
        :param password: 密码
        :param from_email: 发件人邮箱
        :param to_emails: 收件人邮箱列表
        """
        config = get_config()
        
        self.smtp_server = smtp_server or config.get('EMAIL_SMTP_SERVER')
        self.smtp_port = smtp_port or config.get('EMAIL_SMTP_PORT', 587)
        self.username = username or config.get('EMAIL_USERNAME')
        self.password = password or config.get('EMAIL_PASSWORD')
        self.from_email = from_email or config.get('EMAIL_FROM') or self.username
        
        # 处理收件人列表
        if to_emails:
            self.to_emails = to_emails
        else:
            to_email = config.get('EMAIL_TO')
            if to_email:
                if isinstance(to_email, str):
                    self.to_emails = [to_email]
                else:
                    self.to_emails = to_email
            else:
                self.to_emails = []
        
        self.enabled = config.get('EMAIL_ENABLED', False)
        
        # 验证配置
        if self.enabled:
            self._validate_config()
    
    def _validate_config(self):
        """验证邮件配置"""
        required_fields = ['smtp_server', 'username', 'password', 'from_email']
        missing_fields = []
        
        for field in required_fields:
            if not getattr(self, field):
                missing_fields.append(field)
        
        if missing_fields:
            logger.warning(f"Email configuration incomplete. Missing fields: {missing_fields}")
            self.enabled = False
        
        if not self.to_emails:
            logger.warning("No recipient email addresses configured")
    
    def send_message(self, 
                     subject: str, 
                     message: str, 
                     to_emails: List[str] = None,
                     html: bool = False) -> bool:
        """
        发送邮件消息
        
        :param subject: 邮件主题
        :param message: 邮件内容
        :param to_emails: 收件人列表（可选）
        :param html: 是否为HTML格式
        :return: 是否发送成功
        """
        if not self.enabled:
            logger.debug("Email notification is disabled")
            return False
        
        recipients = to_emails or self.to_emails
        if not recipients:
            logger.warning("No recipient email addresses provided")
            return False
        
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject
            
            # 添加邮件内容
            content_type = 'html' if html else 'plain'
            msg.attach(MIMEText(message, content_type, 'utf-8'))
            
            # 发送邮件
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {recipients}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email: {e}")
            return False
    
    def send_error_notification(self, 
                               error_title: str, 
                               error_message: str, 
                               error_details: str = None) -> bool:
        """
        发送错误通知邮件
        
        :param error_title: 错误标题
        :param error_message: 错误消息
        :param error_details: 错误详情
        :return: 是否发送成功
        """
        subject = f"🚨 错误通知: {error_title}"
        
        content = f"""
        <html>
        <body>
            <h2 style="color: #d32f2f;">错误通知</h2>
            <p><strong>错误标题:</strong> {error_title}</p>
            <p><strong>错误消息:</strong> {error_message}</p>
        """
        
        if error_details:
            content += f"""
            <p><strong>错误详情:</strong></p>
            <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
{error_details}
            </pre>
            """
        
        content += """
            <p style="color: #666; font-size: 12px;">
                此邮件由ZVT数据记录器自动发送，请及时处理相关问题。
            </p>
        </body>
        </html>
        """
        
        return self.send_message(subject, content, html=True)
    
    def send_success_notification(self, 
                                 task_name: str, 
                                 details: str = None) -> bool:
        """
        发送成功通知邮件
        
        :param task_name: 任务名称
        :param details: 详细信息
        :return: 是否发送成功
        """
        subject = f"✅ 任务完成: {task_name}"
        
        content = f"""
        <html>
        <body>
            <h2 style="color: #2e7d32;">任务完成通知</h2>
            <p><strong>任务名称:</strong> {task_name}</p>
            <p><strong>状态:</strong> <span style="color: #2e7d32;">成功完成</span></p>
        """
        
        if details:
            content += f"""
            <p><strong>详细信息:</strong></p>
            <div style="background-color: #f5f5f5; padding: 10px; border-radius: 4px;">
                {details}
            </div>
            """
        
        content += """
            <p style="color: #666; font-size: 12px;">
                此邮件由ZVT数据记录器自动发送。
            </p>
        </body>
        </html>
        """
        
        return self.send_message(subject, content, html=True)
    
    def send_data_report(self, 
                        report_title: str, 
                        data_summary: dict) -> bool:
        """
        发送数据报告邮件
        
        :param report_title: 报告标题
        :param data_summary: 数据摘要
        :return: 是否发送成功
        """
        subject = f"📊 数据报告: {report_title}"
        
        content = f"""
        <html>
        <body>
            <h2 style="color: #1976d2;">数据报告</h2>
            <p><strong>报告标题:</strong> {report_title}</p>
            
            <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                <thead>
                    <tr style="background-color: #f5f5f5;">
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">项目</th>
                        <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">值</th>
                    </tr>
                </thead>
                <tbody>
        """
        
        for key, value in data_summary.items():
            content += f"""
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">{key}</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">{value}</td>
                    </tr>
            """
        
        content += """
                </tbody>
            </table>
            
            <p style="color: #666; font-size: 12px;">
                此邮件由ZVT数据记录器自动发送。
            </p>
        </body>
        </html>
        """
        
        return self.send_message(subject, content, html=True)
    
    def test_connection(self) -> bool:
        """
        测试邮件服务器连接
        
        :return: 连接是否成功
        """
        if not self.enabled:
            logger.info("Email is disabled, skipping connection test")
            return False
        
        try:
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
            
            logger.info("Email server connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Email server connection test failed: {e}")
            return False
    
    def get_config_info(self) -> dict:
        """
        获取配置信息
        
        :return: 配置信息字典
        """
        return {
            'enabled': self.enabled,
            'smtp_server': self.smtp_server,
            'smtp_port': self.smtp_port,
            'username': self.username,
            'from_email': self.from_email,
            'to_emails': self.to_emails,
            'has_password': bool(self.password)
        }


__all__ = [
    "EmailInformer",
]
