# ZVT K线数据记录器 - QMT数据源版本

## 📋 概述

这是一个专门使用QMT（迅投QMT）作为数据源的K线数据记录器。该版本将数据源限制为QMT，确保数据的一致性和可靠性。

## 🔧 环境要求

### 必需软件
1. **QMT客户端** - 迅投QMT交易终端
2. **Python 3.9+** - Python运行环境
3. **xtquant模块** - QMT的Python接口模块

### 安装步骤

1. **安装QMT客户端**
   - 从官方网站下载并安装QMT客户端
   - 启动QMT客户端并完成登录

2. **安装Python依赖**
   ```bash
   pip install pandas sqlalchemy requests
   ```

3. **安装xtquant模块**
   ```bash
   # 通常随QMT客户端一起安装
   # 如果没有，请参考QMT官方文档
   ```

## 🚀 快速开始

### 1. 测试QMT连接

在使用数据记录器之前，建议先测试QMT连接：

```bash
python test_qmt_connection.py
```

### 2. 记录K线数据

#### 记录日K线数据
```bash
python start_kline_recorder.py --levels 1d
```

#### 记录多个时间周期
```bash
python start_kline_recorder.py --levels 1d 1h 30m 15m
```

#### 记录指定股票
```bash
python start_kline_recorder.py --levels 1d --codes 600000 000001 000002
```

#### 记录指定时间范围
```bash
python start_kline_recorder.py --levels 1d --start-date 2024-01-01 --end-date 2024-12-31
```

### 3. 查看数据统计

```bash
python start_kline_recorder.py --summary
```

### 4. 查询数据

```bash
python start_kline_recorder.py --query 600000 --level 1d --limit 20
```

## 📊 支持的时间周期

- `1d` - 日K线
- `1h` - 1小时K线
- `30m` - 30分钟K线
- `15m` - 15分钟K线
- `5m` - 5分钟K线
- `1m` - 1分钟K线

## 🗄️ 数据存储

### 数据库文件
- 默认存储路径：`./kline_data/`
- 数据库文件：`qmt_stock_kline_qmt.db`
- 格式：SQLite数据库

### 数据表结构

#### 股票基本信息表 (stock)
- `id` - 股票ID
- `code` - 股票代码
- `name` - 股票名称
- `exchange` - 交易所
- `sector` - 板块
- `industry` - 行业

#### K线数据表 (stock_kline_*)
- `timestamp` - 时间戳
- `open` - 开盘价
- `high` - 最高价
- `low` - 最低价
- `close` - 收盘价
- `volume` - 成交量
- `turnover` - 成交额
- `qfq_*` - 前复权价格（仅日K线）
- `change_pct` - 涨跌幅（仅日K线）
- `turnover_rate` - 换手率（仅日K线）

## ⚙️ 配置选项

### 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--levels` | 时间周期列表 | `--levels 1d 1h 30m` |
| `--codes` | 股票代码列表 | `--codes 600000 000001` |
| `--start-date` | 开始日期 | `--start-date 2024-01-01` |
| `--end-date` | 结束日期 | `--end-date 2024-12-31` |
| `--force-update` | 强制更新 | `--force-update` |
| `--sleeping-time` | 休眠时间（秒） | `--sleeping-time 2` |
| `--data-path` | 数据存储路径 | `--data-path ./data` |
| `--summary` | 显示数据统计 | `--summary` |
| `--query` | 查询股票数据 | `--query 600000` |
| `--verbose` | 详细日志 | `--verbose` |

## 🔍 故障排除

### 常见问题

1. **QMT模块导入失败**
   ```
   ImportError: No module named 'xtquant'
   ```
   - 确保QMT客户端已正确安装
   - 检查Python环境是否正确配置

2. **QMT连接失败**
   ```
   QMT连接失败: Connection refused
   ```
   - 确保QMT客户端已启动并登录
   - 检查网络连接状态

3. **数据获取失败**
   ```
   从QMT获取K线数据失败
   ```
   - 检查股票代码是否正确
   - 确认时间范围是否合理
   - 检查QMT客户端权限

### 调试方法

1. **启用详细日志**
   ```bash
   python start_kline_recorder.py --levels 1d --verbose
   ```

2. **测试单只股票**
   ```bash
   python start_kline_recorder.py --levels 1d --codes 000001
   ```

3. **检查QMT连接**
   ```bash
   python test_qmt_connection.py
   ```

## 📝 注意事项

1. **QMT客户端状态**
   - 使用前必须启动QMT客户端并完成登录
   - 确保QMT客户端连接到服务器

2. **数据权限**
   - 某些数据可能需要特定的权限或订阅
   - 请确认您的QMT账户具有相应权限

3. **网络连接**
   - 需要稳定的网络连接
   - 建议在网络状况良好时进行数据记录

4. **数据频率限制**
   - QMT可能有API调用频率限制
   - 可以通过`--sleeping-time`参数调整请求间隔

## 🤝 技术支持

如果遇到问题，请：

1. 首先运行连接测试：`python test_qmt_connection.py`
2. 检查QMT客户端状态和日志
3. 查看详细错误日志：`--verbose`参数
4. 参考QMT官方文档

## 📄 许可证

本项目基于原ZVT项目，遵循相同的开源许可证。
