# -*- coding: utf-8 -*-
"""
ZVT Data Recorder - 独立的数据记录库

这是从ZVT项目中提取的数据记录功能，提供了完整的数据记录、存储和管理能力。

主要功能：
- 数据记录器基础框架
- 时间序列数据记录
- 数据库会话管理
- 数据模型和Schema管理
- 状态管理和持久化
- 通知和错误处理

作者: 基于ZVT项目提取
版本: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "ZVT Data Recorder Team"

# 导入核心模块
from .core import *
from .database import *
from .utils import *
from .types import *

# 导入主要类
from .core.recorder import Recorder, EntityEventRecorder, TimeSeriesDataRecorder
from .core.schema import Mixin, TradableEntity
from .database.session import get_db_session, get_db_engine
from .types.enums import IntervalLevel, AdjustType, TradableType, Exchange

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    
    # 核心记录器类
    "Recorder",
    "EntityEventRecorder", 
    "TimeSeriesDataRecorder",
    
    # 数据模型基类
    "Mixin",
    "TradableEntity",
    
    # 数据库管理
    "get_db_session",
    "get_db_engine",
    
    # 枚举类型
    "IntervalLevel",
    "AdjustType", 
    "TradableType",
    "Exchange",
]
