#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ZVT Data Recorder 命令行工具

提供命令行接口来管理和运行数据记录器。
"""

import argparse
import logging
import sys
from typing import List

from .config import init_config, init_logging, get_config
from .database import get_providers, list_registered_entities, list_registered_schemas, validate_registration
from .utils import list_available_recorders
from .database.engine import get_engine_stats, cleanup_engines, test_db_connection, get_db_engine


def setup_logging(verbose: bool = False):
    """设置日志"""
    level = 'DEBUG' if verbose else 'INFO'
    init_config({'LOG_LEVEL': level})
    init_logging()


def cmd_info(args):
    """显示系统信息"""
    print("🚀 ZVT Data Recorder 系统信息")
    print("=" * 50)
    
    # 配置信息
    config = get_config()
    print(f"\n📋 配置信息:")
    print(f"- 数据路径: {config.DATA_PATH}")
    print(f"- 数据库类型: {config.DB_ENGINE_TYPE}")
    print(f"- 日志级别: {config.LOG_LEVEL}")
    print(f"- 邮件通知: {'启用' if config.EMAIL_ENABLED else '禁用'}")
    
    # 注册信息
    try:
        providers = get_providers()
        print(f"\n🔌 注册的提供者 ({len(providers)}):")
        for provider in providers:
            print(f"- {provider}")
        
        entities = list_registered_entities()
        print(f"\n🏢 注册的实体类型 ({len(entities)}):")
        for entity in entities:
            print(f"- {entity['entity_type']}: {entity['schema_class']} (提供者: {entity['providers']})")
        
        schemas = list_registered_schemas()
        print(f"\n📊 注册的数据模式 ({len(schemas)}):")
        for schema in schemas:
            print(f"- {schema['schema_class']}: {schema['db_name']} (提供者: {schema['providers']})")
        
        # 验证注册信息
        validation = validate_registration()
        print(f"\n✅ 注册验证:")
        print(f"- 状态: {'通过' if validation['valid'] else '失败'}")
        if validation['issues']:
            print("- 问题:")
            for issue in validation['issues']:
                print(f"  * {issue}")
        
        print(f"- 统计: {validation['summary']}")
        
    except Exception as e:
        print(f"❌ 获取注册信息失败: {e}")


def cmd_list_recorders(args):
    """列出可用的记录器"""
    print("📈 可用的数据记录器")
    print("=" * 50)
    
    try:
        recorders = list_available_recorders()
        
        if not recorders:
            print("没有找到注册的记录器")
            return
        
        for recorder in recorders:
            status = "✅" if recorder['valid'] else "❌"
            print(f"\n{status} {recorder['class_name']}")
            print(f"   提供者: {recorder['provider']}")
            print(f"   数据模式: {recorder.get('data_schema_name', 'N/A')}")
            print(f"   表名: {recorder.get('table_name', 'N/A')}")
            
            if recorder['issues']:
                print(f"   问题: {', '.join(recorder['issues'])}")
    
    except Exception as e:
        print(f"❌ 列出记录器失败: {e}")


def cmd_test_db(args):
    """测试数据库连接"""
    print("🔍 测试数据库连接")
    print("=" * 50)
    
    try:
        providers = get_providers()
        
        if not providers:
            print("没有找到注册的提供者")
            return
        
        for provider in providers:
            print(f"\n测试提供者: {provider}")
            
            try:
                # 尝试获取引擎并测试连接
                engine = get_db_engine(provider=provider, db_name="test")
                
                if test_db_connection(engine):
                    print(f"✅ {provider}: 连接成功")
                else:
                    print(f"❌ {provider}: 连接失败")
                    
            except Exception as e:
                print(f"❌ {provider}: 连接错误 - {e}")
    
    except Exception as e:
        print(f"❌ 测试数据库连接失败: {e}")


def cmd_stats(args):
    """显示统计信息"""
    print("📊 系统统计信息")
    print("=" * 50)
    
    try:
        # 引擎统计
        stats = get_engine_stats()
        print(f"\n🔧 数据库引擎统计:")
        
        if not stats:
            print("没有活跃的数据库引擎")
        else:
            for engine_key, engine_stats in stats.items():
                print(f"\n- {engine_key}:")
                if 'error' in engine_stats:
                    print(f"  错误: {engine_stats['error']}")
                else:
                    print(f"  连接池大小: {engine_stats.get('size', 'N/A')}")
                    print(f"  已签入: {engine_stats.get('checked_in', 'N/A')}")
                    print(f"  已签出: {engine_stats.get('checked_out', 'N/A')}")
                    print(f"  溢出: {engine_stats.get('overflow', 'N/A')}")
        
        # 注册统计
        validation = validate_registration()
        summary = validation['summary']
        print(f"\n📋 注册统计:")
        print(f"- 提供者数量: {summary['providers']}")
        print(f"- 实体类型数量: {summary['entity_types']}")
        print(f"- 数据模式数量: {summary['schemas']}")
        print(f"- 数据库数量: {summary['databases']}")
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")


def cmd_cleanup(args):
    """清理资源"""
    print("🧹 清理系统资源")
    print("=" * 50)
    
    try:
        cleanup_engines()
        print("✅ 数据库引擎清理完成")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")


def cmd_run_example(args):
    """运行示例"""
    print("🎯 运行基础示例")
    print("=" * 50)
    
    try:
        # 导入并运行基础示例
        import os
        import sys
        
        # 添加examples目录到路径
        examples_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'examples')
        if os.path.exists(examples_dir):
            sys.path.insert(0, examples_dir)
            
            if args.example == 'basic':
                import basic_usage
                basic_usage.main()
            elif args.example == 'advanced':
                import advanced_usage
                advanced_usage.main()
            else:
                print(f"未知示例: {args.example}")
        else:
            print("示例目录不存在")
            
    except ImportError as e:
        print(f"❌ 导入示例失败: {e}")
        print("请确保示例文件存在且可访问")
    except Exception as e:
        print(f"❌ 运行示例失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="ZVT Data Recorder 命令行工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  zvt-data-recorder info                    # 显示系统信息
  zvt-data-recorder list-recorders          # 列出可用记录器
  zvt-data-recorder test-db                 # 测试数据库连接
  zvt-data-recorder stats                   # 显示统计信息
  zvt-data-recorder run-example basic       # 运行基础示例
  zvt-data-recorder cleanup                 # 清理资源
        """
    )
    
    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='启用详细输出'
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # info 命令
    info_parser = subparsers.add_parser('info', help='显示系统信息')
    info_parser.set_defaults(func=cmd_info)
    
    # list-recorders 命令
    list_parser = subparsers.add_parser('list-recorders', help='列出可用的记录器')
    list_parser.set_defaults(func=cmd_list_recorders)
    
    # test-db 命令
    test_parser = subparsers.add_parser('test-db', help='测试数据库连接')
    test_parser.set_defaults(func=cmd_test_db)
    
    # stats 命令
    stats_parser = subparsers.add_parser('stats', help='显示统计信息')
    stats_parser.set_defaults(func=cmd_stats)
    
    # cleanup 命令
    cleanup_parser = subparsers.add_parser('cleanup', help='清理系统资源')
    cleanup_parser.set_defaults(func=cmd_cleanup)
    
    # run-example 命令
    example_parser = subparsers.add_parser('run-example', help='运行示例')
    example_parser.add_argument(
        'example',
        choices=['basic', 'advanced'],
        help='要运行的示例'
    )
    example_parser.set_defaults(func=cmd_run_example)
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.verbose)
    
    # 执行命令
    if hasattr(args, 'func'):
        try:
            args.func(args)
        except KeyboardInterrupt:
            print("\n\n⚠️ 用户中断操作")
            sys.exit(1)
        except Exception as e:
            print(f"\n❌ 命令执行失败: {e}")
            if args.verbose:
                import traceback
                traceback.print_exc()
            sys.exit(1)
    else:
        parser.print_help()


if __name__ == '__main__':
    main()
