# -*- coding: utf-8 -*-
"""
测试增强功能
测试数据验证机制、统计信息跟踪和QMT集成功能
"""

import unittest
import logging
import sys
import os
from datetime import datetime
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import pandas as pd
from sqlalchemy import Column, String, Float, DateTime
from sqlalchemy.ext.declarative import declarative_base

from zvt_data_recorder.core.schema import Mixin
from zvt_data_recorder.core.recorder import Recorder
from zvt_data_recorder.recorders.qmt_recorder import (
    QmtKdataRecorder, QmtConnectionManager, QmtDataValidator
)

# 创建数据库基类
Base = declarative_base()

# 配置测试日志
logging.basicConfig(level=logging.DEBUG)


class TestDataModel(Base, Mixin):
    """测试数据模型"""

    __tablename__ = 'test_data'

    code = Column(String(20))
    name = Column(String(100))
    price = Column(Float)
    
    def _validate_business_logic(self) -> bool:
        """测试业务逻辑验证"""
        if self.price and self.price < 0:
            return False
        return True
    
    def _clean_business_data(self):
        """测试业务数据清洗"""
        if self.code:
            self.code = self.code.upper().strip()


class TestRecorder(Recorder):
    """测试记录器"""
    
    provider = "test"
    data_schema = TestDataModel
    
    def _run_impl(self):
        """测试实现"""
        # 模拟处理3个实体
        entities = ['ENTITY1', 'ENTITY2', 'ENTITY3']
        
        def process_entity(entity):
            if entity == 'ENTITY2':
                raise Exception("模拟错误")
            return 10  # 返回记录数
        
        return self.process_entities_with_stats(
            entities=entities,
            process_func=process_entity,
            progress_interval=2
        )


class TestDataValidation(unittest.TestCase):
    """测试数据验证机制"""
    
    def setUp(self):
        self.test_data = TestDataModel()
    
    def test_required_fields_validation(self):
        """测试必要字段验证"""
        # 缺少必要字段
        self.assertFalse(self.test_data.validate_data())
        
        # 填充必要字段
        self.test_data.id = "test_id"
        self.test_data.entity_id = "test_entity"
        self.test_data.timestamp = datetime.now()
        
        self.assertTrue(self.test_data.validate_data())
    
    def test_data_type_validation(self):
        """测试数据类型验证"""
        self.test_data.id = "test_id"
        self.test_data.entity_id = "test_entity"
        self.test_data.timestamp = "invalid_timestamp"  # 错误类型
        
        self.assertFalse(self.test_data.validate_data())
    
    def test_business_logic_validation(self):
        """测试业务逻辑验证"""
        self.test_data.id = "test_id"
        self.test_data.entity_id = "test_entity"
        self.test_data.timestamp = datetime.now()
        self.test_data.price = -10  # 负价格，应该验证失败
        
        self.assertFalse(self.test_data.validate_data())
        
        self.test_data.price = 10  # 正价格
        self.assertTrue(self.test_data.validate_data())
    
    def test_data_cleaning(self):
        """测试数据清洗"""
        self.test_data.id = "  test_id  "
        self.test_data.entity_id = "  test_entity  "
        self.test_data.code = "  abc123  "
        
        self.test_data.clean_data()
        
        self.assertEqual(self.test_data.id, "test_id")
        self.assertEqual(self.test_data.entity_id, "test_entity")
        self.assertEqual(self.test_data.code, "ABC123")  # 应该转为大写
    
    def test_validate_and_clean(self):
        """测试验证和清洗组合"""
        self.test_data.id = "  test_id  "
        self.test_data.entity_id = "  test_entity  "
        self.test_data.timestamp = datetime.now()
        self.test_data.code = "  abc123  "
        self.test_data.price = 10
        
        self.assertTrue(self.test_data.validate_and_clean())
        self.assertEqual(self.test_data.code, "ABC123")
    
    def test_to_dict(self):
        """测试转换为字典"""
        self.test_data.id = "test_id"
        self.test_data.entity_id = "test_entity"
        self.test_data.timestamp = datetime.now()
        self.test_data.code = "ABC123"
        self.test_data.price = 10.5
        
        result = self.test_data.to_dict()
        
        self.assertIsInstance(result, dict)
        self.assertEqual(result['id'], "test_id")
        self.assertEqual(result['code'], "ABC123")
        self.assertEqual(result['price'], 10.5)


class TestStatisticsTracking(unittest.TestCase):
    """测试统计信息跟踪"""
    
    @patch('zvt_data_recorder.database.session.get_db_session')
    def setUp(self, mock_session):
        mock_session.return_value = Mock()
        self.recorder = TestRecorder(
            force_update=False,
            sleeping_time=0,  # 测试时不休眠
            ignore_failed=True
        )
    
    def test_stats_initialization(self):
        """测试统计信息初始化"""
        stats = self.recorder.stats
        
        self.assertEqual(stats['processed_count'], 0)
        self.assertEqual(stats['success_count'], 0)
        self.assertEqual(stats['error_count'], 0)
        self.assertEqual(stats['skipped_count'], 0)
        self.assertEqual(stats['total_records'], 0)
        self.assertIsNone(stats['start_time'])
        self.assertIsNone(stats['end_time'])
    
    def test_record_success(self):
        """测试记录成功信息"""
        self.recorder._record_success("test_entity", 5)
        
        self.assertEqual(self.recorder.stats['success_count'], 1)
        self.assertEqual(self.recorder.stats['total_records'], 5)
        self.assertIn("test_entity", self.recorder.stats['entities_processed'])
    
    def test_record_error(self):
        """测试记录错误信息"""
        error = Exception("测试错误")
        self.recorder._record_error("test_entity", error)
        
        self.assertEqual(len(self.recorder.stats['errors']), 1)
        self.assertIn("test_entity", self.recorder.stats['errors'][0])
    
    def test_stats_summary(self):
        """测试统计信息摘要"""
        # 模拟一些统计数据
        self.recorder.stats['processed_count'] = 10
        self.recorder.stats['success_count'] = 8
        self.recorder.stats['error_count'] = 2
        self.recorder.stats['total_records'] = 80
        self.recorder.stats['start_time'] = 1000
        self.recorder.stats['end_time'] = 1010
        
        summary = self.recorder.get_stats_summary()
        
        self.assertEqual(summary['success_rate'], 80.0)
        self.assertEqual(summary['error_rate'], 20.0)
        self.assertEqual(summary['elapsed_time'], 10)
        self.assertEqual(summary['throughput'], 8.0)  # 80 records / 10 seconds
    
    @patch('time.sleep')  # 避免实际休眠
    def test_process_entities_with_stats(self, mock_sleep):
        """测试带统计的实体处理"""
        entities = ['entity1', 'entity2', 'entity3']
        
        def mock_process_func(entity):
            if entity == 'entity2':
                raise Exception("模拟错误")
            return 5
        
        unfinished = self.recorder.process_entities_with_stats(
            entities=entities,
            process_func=mock_process_func,
            progress_interval=2
        )
        
        # 检查统计信息
        self.assertEqual(self.recorder.stats['processed_count'], 3)
        self.assertEqual(self.recorder.stats['success_count'], 2)
        self.assertEqual(self.recorder.stats['error_count'], 1)
        self.assertEqual(self.recorder.stats['total_records'], 10)  # 2 * 5


class TestQmtIntegration(unittest.TestCase):
    """测试QMT集成功能"""
    
    def test_connection_manager(self):
        """测试QMT连接管理器"""
        manager = QmtConnectionManager()
        
        # 测试导入失败的情况
        with patch('builtins.__import__', side_effect=ImportError("No module")):
            with self.assertRaises(RuntimeError):
                manager.get_xt_client()
    
    @patch('zvt_data_recorder.recorders.qmt_recorder.xt')
    def test_connection_check(self, mock_xt):
        """测试连接检查"""
        manager = QmtConnectionManager()
        manager._xt = mock_xt
        
        # 模拟连接正常
        mock_xt.is_connected.return_value = True
        self.assertTrue(manager.check_connection())
        
        # 模拟连接失败
        mock_xt.is_connected.return_value = False
        self.assertFalse(manager.check_connection())
    
    def test_data_validator(self):
        """测试数据验证器"""
        validator = QmtDataValidator()
        
        # 测试有效的K线数据
        valid_record = {
            'open': 10.0,
            'high': 11.0,
            'low': 9.0,
            'close': 10.5,
            'volume': 1000
        }
        self.assertTrue(validator.validate_kdata_record(valid_record))
        
        # 测试无效的K线数据（最高价小于最低价）
        invalid_record = {
            'open': 10.0,
            'high': 9.0,  # 错误：最高价小于最低价
            'low': 11.0,
            'close': 10.5,
            'volume': 1000
        }
        self.assertFalse(validator.validate_kdata_record(invalid_record))
        
        # 测试缺少必要字段
        incomplete_record = {
            'open': 10.0,
            # 缺少其他必要字段
        }
        self.assertFalse(validator.validate_kdata_record(incomplete_record))
    
    @patch('zvt_data_recorder.database.session.get_db_session')
    def test_qmt_recorder_initialization(self, mock_session):
        """测试QMT记录器初始化"""
        mock_session.return_value = Mock()
        
        recorder = QmtKdataRecorder(
            level='1d',
            adjust_type='qfq'
        )
        
        self.assertEqual(recorder.level, '1d')
        self.assertEqual(recorder.adjust_type, 'qfq')
        self.assertIsInstance(recorder.connection_manager, QmtConnectionManager)
        self.assertIsInstance(recorder.data_validator, QmtDataValidator)


if __name__ == '__main__':
    unittest.main()
