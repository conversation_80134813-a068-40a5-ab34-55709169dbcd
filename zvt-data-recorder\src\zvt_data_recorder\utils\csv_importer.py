# -*- coding: utf-8 -*-
"""
CSV数据导入工具

提供通用的CSV文件导入功能，支持股票K线数据的导入。
包括数据验证、格式转换、字段映射等功能。
"""

import logging
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from ..core.schema import Mixin, TradableEntity
from ..utils.time_utils import to_pd_timestamp
from ..utils.data_utils import to_float
from ..utils.str_utils import normalize_code

logger = logging.getLogger(__name__)


class CSVImporter:
    """CSV数据导入器基类"""
    
    def __init__(self, provider: str = "csv", encoding: str = "gbk"):
        """
        初始化CSV导入器
        
        :param provider: 数据提供者名称
        :param encoding: CSV文件编码
        """
        self.provider = provider
        self.encoding = encoding
        self.logger = logger
        
    def read_csv(self, file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
        """
        读取CSV文件
        
        :param file_path: CSV文件路径
        :param kwargs: pandas.read_csv的其他参数
        :return: DataFrame
        """
        try:
            # 默认参数
            default_kwargs = {
                'encoding': self.encoding,
                'skiprows': 1,  # 跳过第一行说明文字
            }
            default_kwargs.update(kwargs)
            
            df = pd.read_csv(file_path, **default_kwargs)
            self.logger.info(f"成功读取CSV文件: {file_path}, 数据形状: {df.shape}")
            return df
            
        except Exception as e:
            self.logger.error(f"读取CSV文件失败: {file_path}, 错误: {e}")
            raise
    
    def validate_dataframe(self, df: pd.DataFrame, required_columns: List[str]) -> bool:
        """
        验证DataFrame结构
        
        :param df: 待验证的DataFrame
        :param required_columns: 必需的列名列表
        :return: 是否验证通过
        """
        if df is None or df.empty:
            self.logger.error("DataFrame为空")
            return False
        
        missing_columns = set(required_columns) - set(df.columns)
        if missing_columns:
            self.logger.error(f"缺少必需的列: {missing_columns}")
            return False
        
        self.logger.info(f"DataFrame验证通过，包含 {len(df)} 行数据")
        return True
    
    def clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗DataFrame数据
        
        :param df: 原始DataFrame
        :return: 清洗后的DataFrame
        """
        # 删除空行
        df = df.dropna(how='all')
        
        # 重置索引
        df = df.reset_index(drop=True)
        
        self.logger.info(f"数据清洗完成，剩余 {len(df)} 行数据")
        return df


class StockKlineCSVImporter(CSVImporter):
    """股票K线数据CSV导入器"""
    
    # CSV字段到数据库字段的映射
    FIELD_MAPPING = {
        '股票代码': 'code',
        '股票名称': 'name', 
        '交易日期': 'timestamp',
        '开盘价': 'open',
        '最高价': 'high',
        '最低价': 'low',
        '收盘价': 'close',
        '前收盘价': 'pre_close',
        '成交量': 'volume',
        '成交额': 'turnover',
        '流通市值': 'market_cap',
        '总市值': 'total_market_cap'
    }
    
    # 必需的CSV列
    REQUIRED_COLUMNS = [
        '股票代码', '股票名称', '交易日期', '开盘价', 
        '最高价', '最低价', '收盘价', '成交量', '成交额'
    ]
    
    def __init__(self, stock_schema, kline_schema, **kwargs):
        """
        初始化股票K线CSV导入器
        
        :param stock_schema: 股票实体数据模型类
        :param kline_schema: K线数据模型类
        """
        super().__init__(**kwargs)
        self.stock_schema = stock_schema
        self.kline_schema = kline_schema
        
    def import_from_csv(self, file_path: Union[str, Path], 
                       import_stocks: bool = True,
                       import_klines: bool = True) -> Dict[str, Any]:
        """
        从CSV文件导入股票K线数据
        
        :param file_path: CSV文件路径
        :param import_stocks: 是否导入股票基础信息
        :param import_klines: 是否导入K线数据
        :return: 导入结果统计
        """
        try:
            self.logger.info(f"开始导入CSV文件: {file_path}")
            
            # 读取CSV文件
            df = self.read_csv(file_path)
            
            # 验证数据结构
            if not self.validate_dataframe(df, self.REQUIRED_COLUMNS):
                raise ValueError("CSV文件结构验证失败")
            
            # 清洗数据
            df = self.clean_dataframe(df)
            
            # 转换数据格式
            df = self.transform_dataframe(df)
            
            results = {
                'file_path': str(file_path),
                'total_rows': len(df),
                'stocks_imported': 0,
                'klines_imported': 0,
                'errors': []
            }
            
            # 导入股票基础信息
            if import_stocks:
                stock_count = self.import_stock_entities(df)
                results['stocks_imported'] = stock_count
                
            # 导入K线数据
            if import_klines:
                kline_count = self.import_kline_data(df)
                results['klines_imported'] = kline_count
            
            self.logger.info(f"CSV导入完成: {results}")
            return results
            
        except Exception as e:
            self.logger.error(f"CSV导入失败: {e}")
            raise
    
    def transform_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        转换DataFrame数据格式
        
        :param df: 原始DataFrame
        :return: 转换后的DataFrame
        """
        df = df.copy()
        
        # 转换日期格式
        df['交易日期'] = pd.to_datetime(df['交易日期'])
        
        # 标准化股票代码格式
        df['股票代码'] = df['股票代码'].apply(self.normalize_stock_code)
        
        # 生成entity_id
        df['entity_id'] = df['股票代码'].apply(self.generate_entity_id)
        
        # 数值类型转换
        numeric_columns = ['开盘价', '最高价', '最低价', '收盘价', '前收盘价', 
                          '成交量', '成交额', '流通市值', '总市值']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = df[col].apply(to_float)
        
        self.logger.info("数据格式转换完成")
        return df
    
    def normalize_stock_code(self, code: str) -> str:
        """
        标准化股票代码格式
        
        :param code: 原始股票代码 (如: sz002436)
        :return: 标准化后的股票代码 (如: 002436)
        """
        if not code:
            return code
            
        code = str(code).upper().strip()
        
        # 移除前缀
        if code.startswith('SZ'):
            return code[2:]
        elif code.startswith('SH'):
            return code[2:]
        
        return code
    
    def generate_entity_id(self, code: str) -> str:
        """
        生成实体ID

        :param code: 股票代码
        :return: 实体ID
        """
        normalized_code = self.normalize_stock_code(code)

        # 根据代码判断交易所
        if normalized_code.startswith('6'):
            return f"stock_sh_{normalized_code}"
        else:
            return f"stock_sz_{normalized_code}"

    def import_stock_entities(self, df: pd.DataFrame) -> int:
        """
        导入股票基础信息

        :param df: 包含股票信息的DataFrame
        :return: 导入的股票数量
        """
        try:
            # 延迟导入避免循环依赖
            from ..database.session import get_db_session

            # 获取唯一的股票信息
            stock_info = df[['股票代码', '股票名称', 'entity_id']].drop_duplicates()

            session = get_db_session(provider=self.provider, data_schema=self.stock_schema)
            imported_count = 0

            for _, row in stock_info.iterrows():
                try:
                    # 检查股票是否已存在
                    existing_stock = session.query(self.stock_schema).filter(
                        self.stock_schema.id == row['entity_id']
                    ).first()

                    if existing_stock:
                        # 更新股票名称（可能有变化）
                        existing_stock.name = row['股票名称']
                        existing_stock.updated_at = datetime.now()
                    else:
                        # 创建新股票记录
                        stock = self.stock_schema()
                        stock.id = row['entity_id']
                        stock.entity_id = row['entity_id']
                        stock.code = row['股票代码']
                        stock.name = row['股票名称']

                        # 根据代码判断交易所
                        if row['股票代码'].startswith('6'):
                            stock.exchange = 'sh'
                        else:
                            stock.exchange = 'sz'

                        stock.entity_type = 'stock'
                        stock.provider = self.provider
                        stock.timestamp = datetime.now()
                        stock.created_at = datetime.now()
                        stock.updated_at = datetime.now()

                        session.add(stock)
                        imported_count += 1

                    session.commit()

                except Exception as e:
                    session.rollback()
                    self.logger.error(f"导入股票 {row['股票代码']} 失败: {e}")
                    continue

            session.close()
            self.logger.info(f"成功导入 {imported_count} 只股票基础信息")
            return imported_count

        except Exception as e:
            self.logger.error(f"导入股票基础信息失败: {e}")
            raise

    def import_kline_data(self, df: pd.DataFrame) -> int:
        """
        导入K线数据

        :param df: 包含K线数据的DataFrame
        :return: 导入的K线记录数量
        """
        try:
            # 延迟导入避免循环依赖
            from ..database.session import get_db_session

            session = get_db_session(provider=self.provider, data_schema=self.kline_schema)
            imported_count = 0

            for _, row in df.iterrows():
                try:
                    # 生成K线记录ID
                    kline_id = f"{row['entity_id']}_{row['交易日期'].strftime('%Y-%m-%d')}"

                    # 检查记录是否已存在
                    existing_kline = session.query(self.kline_schema).filter(
                        self.kline_schema.id == kline_id
                    ).first()

                    if existing_kline:
                        # 更新现有记录
                        self.update_kline_record(existing_kline, row)
                    else:
                        # 创建新记录
                        kline = self.kline_schema()
                        kline.id = kline_id
                        kline.entity_id = row['entity_id']
                        kline.timestamp = row['交易日期']

                        # 填充K线数据
                        self.fill_kline_data(kline, row)

                        session.add(kline)
                        imported_count += 1

                    # 批量提交（每1000条提交一次）
                    if imported_count % 1000 == 0:
                        session.commit()
                        self.logger.info(f"已导入 {imported_count} 条K线数据")

                except Exception as e:
                    session.rollback()
                    self.logger.error(f"导入K线数据失败: {row['entity_id']} {row['交易日期']}, 错误: {e}")
                    continue

            # 最终提交
            session.commit()
            session.close()

            self.logger.info(f"成功导入 {imported_count} 条K线数据")
            return imported_count

        except Exception as e:
            self.logger.error(f"导入K线数据失败: {e}")
            raise

    def fill_kline_data(self, kline_obj, row):
        """
        填充K线数据对象

        :param kline_obj: K线数据对象
        :param row: DataFrame行数据
        """
        # 基本OHLCV数据
        kline_obj.open = row['开盘价']
        kline_obj.high = row['最高价']
        kline_obj.low = row['最低价']
        kline_obj.close = row['收盘价']
        kline_obj.volume = row['成交量']
        kline_obj.turnover = row['成交额']

        # 其他字段（如果存在）
        if '前收盘价' in row and hasattr(kline_obj, 'pre_close'):
            kline_obj.pre_close = row['前收盘价']

        if '流通市值' in row and hasattr(kline_obj, 'market_cap'):
            kline_obj.market_cap = row['流通市值']

        if '总市值' in row and hasattr(kline_obj, 'total_market_cap'):
            kline_obj.total_market_cap = row['总市值']

        # 计算涨跌幅（如果有前收盘价）
        if '前收盘价' in row and hasattr(kline_obj, 'change_pct'):
            if row['前收盘价'] and row['前收盘价'] > 0:
                kline_obj.change_pct = (row['收盘价'] - row['前收盘价']) / row['前收盘价'] * 100

    def update_kline_record(self, kline_obj, row):
        """
        更新现有K线记录

        :param kline_obj: 现有K线数据对象
        :param row: DataFrame行数据
        """
        self.fill_kline_data(kline_obj, row)
        kline_obj.updated_at = datetime.now()


def import_stock_kline_csv(csv_files: List[Union[str, Path]],
                          stock_schema,
                          kline_schema,
                          provider: str = "csv") -> Dict[str, Any]:
    """
    批量导入股票K线CSV文件的便捷函数

    :param csv_files: CSV文件路径列表
    :param stock_schema: 股票实体数据模型类
    :param kline_schema: K线数据模型类
    :param provider: 数据提供者名称
    :return: 导入结果统计
    """
    importer = StockKlineCSVImporter(stock_schema, kline_schema, provider=provider)

    total_results = {
        'files_processed': 0,
        'total_stocks': 0,
        'total_klines': 0,
        'errors': []
    }

    for csv_file in csv_files:
        try:
            logger.info(f"处理文件: {csv_file}")
            result = importer.import_from_csv(csv_file)

            total_results['files_processed'] += 1
            total_results['total_stocks'] += result['stocks_imported']
            total_results['total_klines'] += result['klines_imported']

        except Exception as e:
            error_msg = f"处理文件 {csv_file} 失败: {e}"
            logger.error(error_msg)
            total_results['errors'].append(error_msg)

    logger.info(f"批量导入完成: {total_results}")
    return total_results
