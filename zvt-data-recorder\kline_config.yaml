# ZVT K线数据记录器配置文件
# 此文件包含所有可配置的选项，支持通过命令行参数覆盖

# 基础配置
basic:
  # 数据存储路径
  data_path: "D:\\MyData\\zvt-data"
  
  # 数据源配置
  data_provider: "qmt"
  
  # 数据库配置
  database:
    db_name: "stock_kline_qmt"
    engine_type: "sqlite"
    echo: false
    pool_size: 5
    max_overflow: 10
    pool_timeout: 30
    pool_recycle: 3600

# 时间周期配置
time_levels:
  # 支持的所有时间周期
  available_levels: ["1d", "1h", "30m", "15m", "5m", "1m"]
  
  # 默认记录的时间周期
  default_levels: ["1d"]
  
  # 时间周期优先级（用于批量记录时的顺序）
  priority_order: ["1d", "1h", "30m", "15m", "5m", "1m"]

# 时间范围配置
time_range:
  # 默认开始日期（null表示自动计算）
  default_start_date: null
  
  # 默认结束日期（null表示当前日期）
  default_end_date: null
  
  # 增量更新时的回溯天数
  incremental_lookback_days: 7
  
  # 全量更新时的默认历史天数
  full_update_history_days: 365

# QMT连接配置
qmt:
  # QMT客户端路径
  client_path: "D:\\QMT\\userdata_mini"
  
  # 账户配置
  account_id: "**********"
  session_id: ""
  
  # 连接参数
  timeout: 30
  retry_times: 5
  max_retry: 3
  
  # 数据获取参数
  adjust_type: "qfq"  # 复权类型：qfq(前复权), hfq(后复权), none(不复权)
  fill_data: true     # 是否填充数据

# 记录器配置
recorder:
  # 默认休眠时间（秒）
  default_sleeping_time: 1
  
  # 批量处理大小
  batch_size: 100
  
  # 默认重试次数
  default_retry_times: 3
  
  # 是否忽略失败的记录
  ignore_failed: true
  
  # 是否强制更新
  force_update: false
  
  # 重复数据处理方式
  fix_duplicate_way: "ignore"  # ignore, replace, skip

# 股票筛选配置
stock_filter:
  # 交易所列表
  exchanges: ["SH", "SZ"]
  
  # 是否排除ST股票
  exclude_st: true
  
  # 是否排除退市股票
  exclude_delisted: true
  
  # 最小市值（亿元，null表示不限制）
  min_market_cap: null
  
  # 指定行业（空列表表示不限制）
  sectors: []
  
  # 指定板块（空列表表示不限制）
  industries: []
  
  # 默认股票代码列表（空列表表示记录所有股票）
  default_codes: []

# 数据更新模式配置
update_mode:
  # 默认更新模式：full(全量), incremental(增量), auto(自动)
  default_mode: "auto"
  
  # 自动模式的判断规则
  auto_mode_rules:
    # 如果数据库为空，使用全量更新
    empty_db_use_full: true
    
    # 如果最后更新时间超过N天，使用全量更新
    full_update_threshold_days: 30
    
    # 增量更新的最大回溯天数
    max_incremental_days: 7

# 日志配置
logging:
  # 日志级别
  level: "INFO"
  
  # 日志格式
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件路径（null表示不写入文件）
  file: null
  
  # 是否显示详细日志
  verbose: false

# 查询配置
query:
  # 默认查询时间周期
  default_level: "1d"
  
  # 默认查询记录数
  default_limit: 10
  
  # 最大查询记录数
  max_limit: 1000

# 交互式模式配置
interactive:
  # 是否启用交互式模式
  enabled: true

  # 交互式模式的默认选项
  defaults:
    update_mode: "auto"
    levels: ["1d"]
    codes: []
    confirm_before_start: true
    require_update_mode_confirmation: true  # 是否需要更新模式二次确认

# 通知配置
notification:
  # 是否启用邮件通知
  email_enabled: false
  
  # SMTP配置
  smtp:
    server: null
    port: 587
    username: null
    password: null
    from: null
    to: null

# 性能配置
performance:
  # 内存使用监控
  memory_monitoring: true
  
  # 进度显示间隔（记录数）
  progress_interval: 100
  
  # 数据库连接池配置
  connection_pool:
    pool_size: 5
    max_overflow: 10
    pool_timeout: 30
    pool_recycle: 3600

# 其他配置
misc:
  # 时区设置
  timezone: "Asia/Shanghai"
  
  # 日期格式
  date_format: "%Y-%m-%d"
  
  # 日期时间格式
  datetime_format: "%Y-%m-%d %H:%M:%S"
  
  # 交易日历
  trading_calendar: "china_sse"
