# -*- coding: utf-8 -*-
"""
QMT数据记录器实现
完整的QMT本地数据采集功能，包括xtquant客户端集成、数据格式转换、连接状态管理和错误处理机制
注意：这是本地数据采集，不涉及任何Web API或网络接口
"""

import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union
from decimal import Decimal

from ..core.recorder import Recorder
from ..core.schema import Mixin
from ..utils.time_utils import to_pd_timestamp, now_pd_timestamp
from ..utils.data_utils import clean_data_frame

logger = logging.getLogger(__name__)


class QmtConnectionManager:
    """QMT连接管理器"""
    
    def __init__(self):
        self._xt = None
        self._connected = False
        self._last_check_time = None
        self._check_interval = 30  # 30秒检查一次连接状态
    
    def get_xt_client(self):
        """获取xtquant客户端"""
        if self._xt is None:
            try:
                import xtquant as xt
                self._xt = xt
                logger.info("成功导入xtquant模块")
            except ImportError as e:
                logger.error(f"无法导入xtquant模块: {e}")
                raise RuntimeError("请确保已安装xtquant模块")
        return self._xt
    
    def check_connection(self, force_check: bool = False) -> bool:
        """检查QMT连接状态"""
        current_time = time.time()
        
        # 如果不是强制检查且距离上次检查时间不足间隔，返回缓存状态
        if not force_check and self._last_check_time:
            if current_time - self._last_check_time < self._check_interval:
                return self._connected
        
        try:
            xt = self.get_xt_client()
            self._connected = xt.is_connected()
            self._last_check_time = current_time
            
            if self._connected:
                logger.debug("QMT连接状态正常")
            else:
                logger.warning("QMT客户端未连接")
                
            return self._connected
            
        except Exception as e:
            logger.error(f"检查QMT连接状态失败: {e}")
            self._connected = False
            self._last_check_time = current_time
            return False
    
    def ensure_connection(self):
        """确保QMT连接正常"""
        if not self.check_connection(force_check=True):
            raise RuntimeError("QMT客户端未连接，请先启动QMT客户端并登录")


class QmtDataValidator:
    """QMT数据验证器"""
    
    @staticmethod
    def validate_kdata_record(record: Dict[str, Any]) -> bool:
        """验证K线数据记录"""
        try:
            # 基础字段检查
            required_fields = ['open', 'high', 'low', 'close']
            for field in required_fields:
                if field not in record or record[field] is None:
                    logger.warning(f"缺少必要字段: {field}")
                    return False
                
                # 检查是否为有效数值
                try:
                    float(record[field])
                except (ValueError, TypeError):
                    logger.warning(f"字段 {field} 不是有效数值: {record[field]}")
                    return False
            
            # 价格合理性检查
            open_price = float(record['open'])
            high_price = float(record['high'])
            low_price = float(record['low'])
            close_price = float(record['close'])
            
            if high_price < low_price:
                logger.warning(f"数据异常：最高价 {high_price} 小于最低价 {low_price}")
                return False
            
            if not (low_price <= open_price <= high_price):
                logger.warning(f"数据异常：开盘价 {open_price} 不在合理范围内")
                return False
                
            if not (low_price <= close_price <= high_price):
                logger.warning(f"数据异常：收盘价 {close_price} 不在合理范围内")
                return False
            
            # 成交量检查
            if 'volume' in record and record['volume'] is not None:
                try:
                    volume = float(record['volume'])
                    if volume < 0:
                        logger.warning(f"数据异常：成交量为负数 {volume}")
                        return False
                except (ValueError, TypeError):
                    logger.warning(f"成交量不是有效数值: {record['volume']}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False
    
    @staticmethod
    def validate_stock_info(stock_info: Dict[str, Any]) -> bool:
        """验证股票信息"""
        try:
            required_fields = ['stock_code', 'stock_name']
            for field in required_fields:
                if field not in stock_info or not stock_info[field]:
                    logger.warning(f"股票信息缺少必要字段: {field}")
                    return False
            
            # 股票代码格式检查
            stock_code = stock_info['stock_code']
            if not isinstance(stock_code, str) or len(stock_code) != 8:
                logger.warning(f"股票代码格式不正确: {stock_code}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"股票信息验证失败: {e}")
            return False


class QmtKdataRecorder(Recorder):
    """QMT K线数据记录器"""
    
    provider = "qmt"
    
    def __init__(self, level: str = '1d', adjust_type: str = 'qfq', 
                 max_retry: int = 3, retry_delay: int = 5, **kwargs):
        super().__init__(**kwargs)
        self.level = level
        self.adjust_type = adjust_type
        self.max_retry = max_retry
        self.retry_delay = retry_delay
        
        # 初始化连接管理器和数据验证器
        self.connection_manager = QmtConnectionManager()
        self.data_validator = QmtDataValidator()
        
        # QMT周期映射
        self.period_map = {
            '1m': '1m',
            '5m': '5m', 
            '15m': '15m',
            '30m': '30m',
            '1h': '1h',
            '1d': '1d',
            '1w': '1w',
            '1M': '1M'
        }
        
        if self.level not in self.period_map:
            raise ValueError(f"不支持的周期: {self.level}")
    
    def initialize(self):
        """初始化记录器"""
        super().initialize()
        self.connection_manager.ensure_connection()
        logger.info(f"QMT K线记录器初始化完成，周期: {self.level}, 复权类型: {self.adjust_type}")
    
    def get_stock_list(self) -> List[str]:
        """获取股票列表"""
        try:
            xt = self.connection_manager.get_xt_client()
            
            # 获取沪深A股列表
            stock_list = []
            
            # 获取沪市A股
            sh_stocks = xt.get_stock_list_in_sector('沪市A股')
            if sh_stocks:
                stock_list.extend([stock['stock_code'] for stock in sh_stocks])
            
            # 获取深市A股
            sz_stocks = xt.get_stock_list_in_sector('深市A股')
            if sz_stocks:
                stock_list.extend([stock['stock_code'] for stock in sz_stocks])
            
            logger.info(f"获取到 {len(stock_list)} 只股票")
            return stock_list
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def get_kdata_with_retry(self, stock_code: str, start_date: str = None, 
                           end_date: str = None, count: int = -1) -> Optional[pd.DataFrame]:
        """带重试机制的K线数据获取"""
        for attempt in range(self.max_retry):
            try:
                # 确保连接正常
                self.connection_manager.ensure_connection()
                xt = self.connection_manager.get_xt_client()
                
                # 调用QMT本地客户端获取数据
                df = xt.get_market_data(
                    stock_list=[stock_code],
                    period=self.period_map[self.level],
                    start_time=start_date,
                    end_time=end_date,
                    count=count,
                    dividend_type=self.adjust_type,
                    fill_data=True
                )
                
                if df is not None and not df.empty:
                    logger.debug(f"成功获取 {stock_code} 的 {len(df)} 条K线数据")
                    return df
                else:
                    logger.warning(f"未获取到 {stock_code} 的K线数据")
                    return None
                    
            except Exception as e:
                logger.warning(f"获取 {stock_code} K线数据失败 (尝试 {attempt + 1}/{self.max_retry}): {e}")
                
                if attempt < self.max_retry - 1:
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"获取 {stock_code} K线数据最终失败")
                    return None
        
        return None
    
    def convert_kdata_to_records(self, df: pd.DataFrame, stock_code: str) -> List[Dict[str, Any]]:
        """将QMT K线数据转换为记录格式"""
        records = []
        
        try:
            for timestamp, row in df.iterrows():
                # 生成唯一ID
                record_id = f"{stock_code}_{self.level}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
                
                record = {
                    'id': record_id,
                    'entity_id': stock_code,
                    'timestamp': timestamp,
                    'code': stock_code,
                    'level': self.level,
                    'open': self._safe_convert_to_decimal(row.get('open')),
                    'high': self._safe_convert_to_decimal(row.get('high')),
                    'low': self._safe_convert_to_decimal(row.get('low')),
                    'close': self._safe_convert_to_decimal(row.get('close')),
                    'volume': self._safe_convert_to_decimal(row.get('volume')),
                    'turnover': self._safe_convert_to_decimal(row.get('amount')),
                    'provider': self.provider,
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
                
                # 数据验证
                if self.data_validator.validate_kdata_record(record):
                    records.append(record)
                else:
                    logger.warning(f"跳过无效的K线数据记录: {record_id}")
        
        except Exception as e:
            logger.error(f"转换K线数据失败: {e}")
        
        return records
    
    def _safe_convert_to_decimal(self, value) -> Optional[Decimal]:
        """安全转换为Decimal类型"""
        if value is None or pd.isna(value):
            return None
        try:
            return Decimal(str(float(value)))
        except (ValueError, TypeError, pd.errors.ParserError):
            return None
    
    def record_stock_kdata(self, stock_code: str, start_date: str = None, 
                          end_date: str = None, count: int = -1) -> List[Dict[str, Any]]:
        """记录单只股票的K线数据"""
        try:
            # 获取K线数据
            df = self.get_kdata_with_retry(stock_code, start_date, end_date, count)
            
            if df is None or df.empty:
                return []
            
            # 转换为记录格式
            records = self.convert_kdata_to_records(df, stock_code)
            
            logger.info(f"成功处理 {stock_code} 的 {len(records)} 条K线数据")
            return records
            
        except Exception as e:
            logger.error(f"记录 {stock_code} K线数据失败: {e}")
            return []


class QmtStockRecorder(Recorder):
    """QMT股票基础信息记录器"""
    
    provider = "qmt"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.connection_manager = QmtConnectionManager()
        self.data_validator = QmtDataValidator()
    
    def initialize(self):
        """初始化记录器"""
        super().initialize()
        self.connection_manager.ensure_connection()
        logger.info("QMT股票信息记录器初始化完成")
    
    def get_stock_info_list(self) -> List[Dict[str, Any]]:
        """获取股票基础信息列表"""
        try:
            xt = self.connection_manager.get_xt_client()
            stock_info_list = []
            
            # 获取沪深A股信息
            for sector in ['沪市A股', '深市A股']:
                stocks = xt.get_stock_list_in_sector(sector)
                if stocks:
                    for stock in stocks:
                        stock_info = {
                            'id': stock['stock_code'],
                            'entity_id': stock['stock_code'],
                            'code': stock['stock_code'],
                            'name': stock['stock_name'],
                            'exchange': 'sh' if stock['stock_code'].endswith('.SH') else 'sz',
                            'entity_type': 'stock',
                            'provider': self.provider,
                            'timestamp': datetime.now(),
                            'created_at': datetime.now(),
                            'updated_at': datetime.now()
                        }
                        
                        # 数据验证
                        if self.data_validator.validate_stock_info(stock_info):
                            stock_info_list.append(stock_info)
            
            logger.info(f"获取到 {len(stock_info_list)} 只股票的基础信息")
            return stock_info_list
            
        except Exception as e:
            logger.error(f"获取股票基础信息失败: {e}")
            return []
