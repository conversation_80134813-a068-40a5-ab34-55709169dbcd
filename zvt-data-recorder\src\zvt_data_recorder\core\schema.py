# -*- coding: utf-8 -*-
"""
数据模型基类

从ZVT项目中提取的数据模型基础类，提供统一的数据访问接口。
"""

import inspect
import logging
from datetime import timedelta, datetime
from typing import List, Union, Type, Dict, Any, Optional

import pandas as pd
from sqlalchemy import Column, String, DateTime, Float
from sqlalchemy.orm import Session

from ..types.enums import IntervalLevel
from ..utils.time_utils import date_and_time, is_same_time, now_pd_timestamp

logger = logging.getLogger(__name__)


class Mixin(object):
    """
    数据模型基类
    
    所有数据模型都应该继承此类，提供统一的数据访问接口。
    """

    #: 主键ID
    id = Column(String, primary_key=True)
    #: 实体ID
    entity_id = Column(String)
    #: 时间戳，对于不同情况可能有不同含义，大多数时候表示'发生时间'
    timestamp = Column(DateTime)

    @classmethod
    def help(cls):
        """打印类的源代码"""
        print(inspect.getsource(cls))

    @classmethod
    def important_cols(cls):
        """返回重要列名列表"""
        return []

    @classmethod
    def time_field(cls):
        """返回时间字段名"""
        return "timestamp"

    def validate_data(self) -> bool:
        """
        数据验证方法

        验证数据的完整性和合理性，子类可以重写此方法添加特定验证逻辑。

        Returns:
            bool: 验证是否通过
        """
        try:
            # 基础字段验证
            if not self._validate_required_fields():
                return False

            # 业务逻辑验证
            if not self._validate_business_logic():
                return False

            # 数据类型验证
            if not self._validate_data_types():
                return False

            return True

        except Exception as e:
            logger.error(f"数据验证失败: {e}")
            return False

    def _validate_required_fields(self) -> bool:
        """验证必要字段"""
        try:
            # 检查主键ID
            if not hasattr(self, 'id') or not self.id:
                logger.warning("id字段不能为空")
                return False

            # 检查实体ID
            if not hasattr(self, 'entity_id') or not self.entity_id:
                logger.warning("entity_id字段不能为空")
                return False

            # 检查时间戳
            if not hasattr(self, 'timestamp') or not self.timestamp:
                logger.warning("timestamp字段不能为空")
                return False

            return True

        except Exception as e:
            logger.error(f"必要字段验证失败: {e}")
            return False

    def _validate_data_types(self) -> bool:
        """验证数据类型"""
        try:
            # 验证时间戳类型
            if hasattr(self, 'timestamp') and self.timestamp:
                if not isinstance(self.timestamp, (datetime, pd.Timestamp)):
                    logger.warning(f"timestamp字段类型不正确: {type(self.timestamp)}")
                    return False

            # 验证字符串字段
            string_fields = ['id', 'entity_id']
            for field in string_fields:
                if hasattr(self, field) and getattr(self, field) is not None:
                    value = getattr(self, field)
                    if not isinstance(value, str):
                        logger.warning(f"{field}字段应为字符串类型: {type(value)}")
                        return False

            return True

        except Exception as e:
            logger.error(f"数据类型验证失败: {e}")
            return False

    def _validate_business_logic(self) -> bool:
        """
        业务逻辑验证，子类可重写

        Returns:
            bool: 验证是否通过
        """
        return True

    def clean_data(self):
        """
        数据清洗方法

        清洗和标准化数据，子类可以重写此方法添加特定清洗逻辑。
        """
        try:
            # 清洗字符串字段
            self._clean_string_fields()

            # 清洗时间字段
            self._clean_datetime_fields()

            # 业务数据清洗
            self._clean_business_data()

        except Exception as e:
            logger.error(f"数据清洗失败: {e}")

    def _clean_string_fields(self):
        """清洗字符串字段"""
        try:
            string_fields = ['id', 'entity_id']

            # 添加动态字符串字段检测
            for attr_name in dir(self):
                if not attr_name.startswith('_'):
                    attr_value = getattr(self, attr_name, None)
                    if isinstance(attr_value, str) and hasattr(self.__class__, attr_name):
                        column = getattr(self.__class__, attr_name, None)
                        if hasattr(column, 'type') and hasattr(column.type, 'python_type'):
                            if column.type.python_type == str:
                                string_fields.append(attr_name)

            # 去除重复
            string_fields = list(set(string_fields))

            for field in string_fields:
                if hasattr(self, field):
                    value = getattr(self, field)
                    if isinstance(value, str):
                        # 去除前后空格
                        cleaned_value = value.strip()
                        # 替换多个空格为单个空格
                        cleaned_value = ' '.join(cleaned_value.split())
                        setattr(self, field, cleaned_value)

        except Exception as e:
            logger.error(f"字符串字段清洗失败: {e}")

    def _clean_datetime_fields(self):
        """清洗时间字段"""
        try:
            if hasattr(self, 'timestamp') and self.timestamp:
                # 确保时间戳为datetime类型
                if isinstance(self.timestamp, str):
                    self.timestamp = pd.to_datetime(self.timestamp)
                elif isinstance(self.timestamp, pd.Timestamp):
                    self.timestamp = self.timestamp.to_pydatetime()

        except Exception as e:
            logger.error(f"时间字段清洗失败: {e}")

    def _clean_business_data(self):
        """
        业务数据清洗，子类可重写
        """
        pass

    def to_dict(self) -> Dict[str, Any]:
        """
        将对象转换为字典

        Returns:
            Dict[str, Any]: 对象的字典表示
        """
        try:
            result = {}

            # 获取所有列属性
            for column in self.__table__.columns:
                column_name = column.name
                value = getattr(self, column_name, None)

                # 处理特殊类型
                if isinstance(value, datetime):
                    result[column_name] = value.isoformat()
                elif isinstance(value, pd.Timestamp):
                    result[column_name] = value.isoformat()
                else:
                    result[column_name] = value

            return result

        except Exception as e:
            logger.error(f"转换为字典失败: {e}")
            return {}

    def validate_and_clean(self) -> bool:
        """
        验证并清洗数据

        Returns:
            bool: 处理是否成功
        """
        try:
            # 先清洗数据
            self.clean_data()

            # 再验证数据
            return self.validate_data()

        except Exception as e:
            logger.error(f"验证和清洗数据失败: {e}")
            return False

    @classmethod
    def register_recorder_cls(cls, provider: str, recorder_cls: Type):
        """
        为模式注册记录器类
        
        :param provider: 提供者名称
        :param recorder_cls: 记录器类
        """
        # 不要将provider_map_recorder作为类字段，应该根据需要为子类创建
        if not hasattr(cls, "provider_map_recorder"):
            cls.provider_map_recorder = {}

        if provider not in cls.provider_map_recorder:
            cls.provider_map_recorder[provider] = recorder_cls

    @classmethod
    def register_provider(cls, provider: str):
        """
        为模式注册提供者
        
        :param provider: 提供者名称
        """
        # 不要将providers作为类字段，应该根据需要为子类创建
        if not hasattr(cls, "providers"):
            cls.providers = []

        if provider not in cls.providers:
            cls.providers.append(provider)

    @classmethod
    def get_providers(cls) -> List[str]:
        """
        获取模式的提供者列表
        
        :return: 提供者列表
        """
        if hasattr(cls, "providers"):
            return cls.providers
        return []

    @classmethod
    def test_data_correctness(cls, provider: str, data_samples: List[Dict]):
        """
        测试数据正确性
        
        :param provider: 提供者
        :param data_samples: 数据样本
        """
        for data in data_samples:
            item = cls.query_data(provider=provider, ids=[data["id"]], return_type="dict")
            print(item)
            for k in data:
                if k == "timestamp":
                    assert is_same_time(item[0][k], data[k])
                else:
                    assert item[0][k] == data[k]

    @classmethod
    def get_by_id(cls, id: str, provider_index: int = 0, provider: str = None):
        """
        根据ID获取单个记录
        
        :param id: 记录ID
        :param provider_index: 提供者索引
        :param provider: 提供者名称
        :return: 记录对象
        """
        from ..database.session import get_by_id

        if not provider:
            providers = cls.get_providers()
            if providers and provider_index < len(providers):
                provider = providers[provider_index]
            else:
                raise ValueError(f"No provider available for {cls.__name__}")
        
        return get_by_id(data_schema=cls, id=id, provider=provider)

    @classmethod
    def query_data(cls, 
                   provider: str = None,
                   ids: List[str] = None,
                   entity_ids: List[str] = None,
                   entity_id: str = None,
                   codes: List[str] = None,
                   code: str = None,
                   level: Union[str, IntervalLevel] = None,
                   provider_index: int = 0,
                   start_timestamp: Union[str, pd.Timestamp] = None,
                   end_timestamp: Union[str, pd.Timestamp] = None,
                   columns: List[str] = None,
                   return_type: str = "df",
                   session: Session = None,
                   order=None,
                   limit: int = None,
                   filters: List = None,
                   time_field: str = None) -> Union[pd.DataFrame, List, Dict]:
        """
        查询数据
        
        :param provider: 数据提供者
        :param ids: ID列表
        :param entity_ids: 实体ID列表
        :param entity_id: 单个实体ID
        :param codes: 代码列表
        :param code: 单个代码
        :param level: 级别
        :param provider_index: 提供者索引
        :param start_timestamp: 开始时间
        :param end_timestamp: 结束时间
        :param columns: 列名列表
        :param return_type: 返回类型 ("df", "dict", "domain")
        :param session: 数据库会话
        :param order: 排序
        :param limit: 限制条数
        :param filters: 过滤条件
        :param time_field: 时间字段
        :return: 查询结果
        """
        from ..database.session import get_data

        if not provider:
            providers = cls.get_providers()
            if providers and provider_index < len(providers):
                provider = providers[provider_index]
            else:
                raise ValueError(f"No provider available for {cls.__name__}")

        # 处理单个值转换为列表
        if entity_id:
            entity_ids = [entity_id] if not entity_ids else entity_ids + [entity_id]
        if code:
            codes = [code] if not codes else codes + [code]

        return get_data(
            data_schema=cls,
            provider=provider,
            ids=ids,
            entity_ids=entity_ids,
            codes=codes,
            level=level,
            start_timestamp=start_timestamp,
            end_timestamp=end_timestamp,
            columns=columns,
            return_type=return_type,
            session=session,
            order=order,
            limit=limit,
            filters=filters,
            time_field=time_field or cls.time_field()
        )

    @classmethod
    def record_data(cls, 
                    provider: str = None,
                    **kwargs):
        """
        记录数据
        
        :param provider: 数据提供者
        :param kwargs: 其他参数
        """
        if not provider:
            providers = cls.get_providers()
            if providers:
                provider = providers[0]
            else:
                raise ValueError(f"No provider available for {cls.__name__}")

        # 获取注册的记录器类
        if hasattr(cls, "provider_map_recorder") and provider in cls.provider_map_recorder:
            recorder_cls = cls.provider_map_recorder[provider]
            recorder = recorder_cls(**kwargs)
            recorder.run()
        else:
            raise ValueError(f"No recorder registered for provider {provider} in {cls.__name__}")

    @classmethod
    def get_storages(cls):
        """
        获取存储引擎列表
        
        :return: 存储引擎列表
        """
        from ..database.engine import get_db_engine
        
        engines = []
        providers = cls.get_providers()
        for provider in providers:
            try:
                engine = get_db_engine(provider=provider, data_schema=cls)
                engines.append(engine)
            except:
                continue
        
        return engines


class TradableEntity(Mixin):
    """
    可交易实体基类
    
    表示可以进行交易的实体，如股票、期货、债券等。
    """

    #: 实体代码
    code = Column(String(length=64))
    #: 实体名称
    name = Column(String(length=128))
    #: 上市时间
    list_date = Column(DateTime)
    #: 退市时间
    end_date = Column(DateTime)

    @classmethod
    def get_trading_intervals(cls):
        """
        获取交易时间间隔
        
        :return: 交易时间间隔列表
        """
        # 默认交易时间（可被子类重写）
        return [
            ("09:30", "11:30"),  # 上午
            ("13:00", "15:00"),  # 下午
        ]

    @classmethod
    def get_close_hour_and_minute(cls):
        """
        获取收盘时间
        
        :return: (小时, 分钟)
        """
        # 默认15:00收盘
        return 15, 0

    def is_trading_time(self, timestamp: pd.Timestamp = None):
        """
        判断是否为交易时间
        
        :param timestamp: 时间戳，默认为当前时间
        :return: 是否为交易时间
        """
        if timestamp is None:
            timestamp = now_pd_timestamp()

        # 检查是否为工作日
        if timestamp.weekday() >= 5:  # 周六、周日
            return False

        # 检查是否在交易时间段内
        current_time = timestamp.time()
        intervals = self.get_trading_intervals()
        
        for start_str, end_str in intervals:
            start_time = pd.Timestamp(start_str).time()
            end_time = pd.Timestamp(end_str).time()
            if start_time <= current_time <= end_time:
                return True
        
        return False


__all__ = [
    "Mixin",
    "TradableEntity",
]
