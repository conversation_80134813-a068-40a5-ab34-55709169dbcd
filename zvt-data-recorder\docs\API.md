# ZVT Data Recorder API 文档

## 核心模块

### 数据模型 (Schema)

#### Mixin

所有数据模型的基类，提供基础字段和统一的数据访问接口。

```python
from zvt_data_recorder import Mixin

class MyDataModel(Base, Mixin):
    __tablename__ = 'my_data'
    
    # 自定义字段
    value = Column(Float)
```

**基础字段:**
- `id`: 主键ID (String)
- `entity_id`: 实体ID (String)  
- `timestamp`: 时间戳 (DateTime)

**主要方法:**

##### `query_data()`
查询数据的核心方法。

```python
@classmethod
def query_data(cls, 
               provider: str = None,
               ids: List[str] = None,
               entity_ids: List[str] = None,
               entity_id: str = None,
               codes: List[str] = None,
               code: str = None,
               level: Union[str, IntervalLevel] = None,
               provider_index: int = 0,
               start_timestamp: Union[str, pd.Timestamp] = None,
               end_timestamp: Union[str, pd.Timestamp] = None,
               columns: List[str] = None,
               return_type: str = "df",
               session: Session = None,
               order=None,
               limit: int = None,
               filters: List = None,
               time_field: str = None) -> Union[pd.DataFrame, List, Dict]
```

**参数:**
- `provider`: 数据提供者名称
- `entity_ids`: 实体ID列表
- `start_timestamp`: 开始时间
- `end_timestamp`: 结束时间
- `return_type`: 返回类型 ("df", "dict", "domain")
- `limit`: 限制返回条数

**示例:**
```python
# 查询DataFrame格式数据
df = MyDataModel.query_data(
    provider="example",
    entity_ids=["entity_1", "entity_2"],
    start_timestamp="2023-01-01",
    end_timestamp="2023-12-31",
    return_type="df"
)

# 查询字典格式数据
data = MyDataModel.query_data(
    provider="example",
    entity_id="entity_1",
    limit=100,
    return_type="dict"
)
```

##### `record_data()`
记录数据的便捷方法。

```python
@classmethod
def record_data(cls, provider: str = None, **kwargs)
```

#### TradableEntity

可交易实体基类，继承自Mixin，用于股票、期货、债券等金融工具。

```python
from zvt_data_recorder import TradableEntity

class Stock(Base, TradableEntity):
    __tablename__ = 'stock'
    
    # 股票特有字段
    market_cap = Column(Float)
    pe_ratio = Column(Float)
```

**额外字段:**
- `code`: 实体代码 (String)
- `name`: 实体名称 (String)
- `list_date`: 上市时间 (DateTime)
- `end_date`: 退市时间 (DateTime)

**主要方法:**

##### `get_trading_intervals()`
获取交易时间间隔。

```python
@classmethod
def get_trading_intervals(cls):
    return [
        ("09:30", "11:30"),  # 上午
        ("13:00", "15:00"),  # 下午
    ]
```

##### `is_trading_time()`
判断是否为交易时间。

```python
def is_trading_time(self, timestamp: pd.Timestamp = None) -> bool
```

### 记录器 (Recorder)

#### Recorder

数据记录器基类，提供数据记录的基础框架。

```python
from zvt_data_recorder import Recorder

class MyRecorder(Recorder):
    provider = "my_provider"
    data_schema = MyDataModel
    
    def record(self, entity, start, end, size, timestamps):
        # 实现数据记录逻辑
        return data_list
```

**核心属性:**
- `provider`: 数据提供者名称
- `data_schema`: 数据模式类
- `entity_provider`: 实体提供者名称
- `entity_schema`: 实体模式类

**主要方法:**

##### `run()`
运行记录器。

```python
def run(self) -> List[str]
```

##### `record()`
记录数据的核心方法，需要子类实现。

```python
def record(self, entity, start, end, size, timestamps):
    """
    :param entity: 实体对象
    :param start: 开始时间
    :param end: 结束时间
    :param size: 数据大小
    :param timestamps: 时间戳列表
    :return: 数据记录列表
    """
    raise NotImplementedError
```

#### TimeSeriesDataRecorder

时间序列数据记录器，继承自Recorder，专门用于时间序列数据。

```python
from zvt_data_recorder import TimeSeriesDataRecorder

class StockPriceRecorder(TimeSeriesDataRecorder):
    provider = "example"
    data_schema = StockPrice
    entity_provider = "example"
    entity_schema = Stock
    
    def record(self, entity, start, end, size, timestamps):
        # 获取股票价格数据
        return price_data
    
    def get_data_map(self):
        # 定义字段映射
        return {
            'date': ('timestamp', pd.to_datetime),
            'price': ('close', float),
        }
```

**额外方法:**

##### `get_data_map()`
定义数据字段映射关系。

```python
def get_data_map(self) -> Dict[str, tuple]:
    return {
        'source_field': ('target_field', transform_function),
    }
```

### 数据库管理

#### 引擎管理

##### `get_db_engine()`
获取数据库引擎。

```python
from zvt_data_recorder.database import get_db_engine

engine = get_db_engine(
    provider="example",
    data_schema=MyDataModel
)
```

##### `create_db_engine()`
创建自定义数据库引擎。

```python
from zvt_data_recorder.database import create_db_engine

engine = create_db_engine(
    db_url="sqlite:///my_data.db",
    echo=True
)
```

#### 会话管理

##### `get_db_session()`
获取数据库会话。

```python
from zvt_data_recorder.database import get_db_session

session = get_db_session(
    provider="example",
    data_schema=MyDataModel
)
```

##### `get_data()`
查询数据。

```python
from zvt_data_recorder.database import get_data

df = get_data(
    data_schema=MyDataModel,
    provider="example",
    entity_ids=["entity_1"],
    start_timestamp="2023-01-01",
    return_type="df"
)
```

#### 注册机制

##### `register_schema()`
注册数据模式。

```python
from zvt_data_recorder.database import register_schema

register_schema(
    providers=["provider1", "provider2"],
    db_name="my_database",
    schema_base=Base,
    entity_type="my_entity"
)
```

##### `register_entity()`
注册实体类型装饰器。

```python
from zvt_data_recorder.database import register_entity

@register_entity("stock")
class Stock(Base, TradableEntity):
    __tablename__ = 'stock'
```

### 工具函数

#### 时间处理

```python
from zvt_data_recorder.utils import (
    to_pd_timestamp, to_time_str, now_pd_timestamp
)

# 转换时间戳
ts = to_pd_timestamp("2023-01-01")

# 格式化时间
time_str = to_time_str(ts, "YYYY-MM-DD")

# 获取当前时间
now = now_pd_timestamp()
```

#### 数据处理

```python
from zvt_data_recorder.utils import (
    pd_is_not_null, index_df, to_float
)

# 检查DataFrame是否不为空
if pd_is_not_null(df):
    # 设置索引
    df = index_df(df, index=['entity_id', 'timestamp'])

# 数据类型转换
value = to_float("123.45")
```

#### 记录器工具

```python
from zvt_data_recorder.utils import (
    run_data_recorder, batch_record_data
)

# 运行单个记录器
run_data_recorder(
    domain=MyDataModel,
    data_provider="example",
    entity_ids=["entity_1"]
)

# 批量记录数据
results = batch_record_data(
    domains=[DataModel1, DataModel2],
    provider="example",
    entity_ids=["entity_1", "entity_2"]
)
```

### 配置管理

#### 配置类

```python
from zvt_data_recorder.config import get_config, init_config

# 获取配置
config = get_config()
data_path = config.DATA_PATH

# 初始化配置
init_config({
    'DATA_PATH': '/path/to/data',
    'EMAIL_ENABLED': True,
    'EMAIL_SMTP_SERVER': 'smtp.gmail.com'
})
```

#### 日志配置

```python
from zvt_data_recorder.config import init_logging

# 初始化日志
init_logging()
```

### 通知系统

#### 邮件通知

```python
from zvt_data_recorder.notifier import EmailInformer

# 创建邮件通知器
email_informer = EmailInformer(
    smtp_server="smtp.gmail.com",
    smtp_port=587,
    username="<EMAIL>",
    password="your-password"
)

# 发送消息
email_informer.send_message(
    subject="测试邮件",
    message="这是一条测试消息"
)

# 发送错误通知
email_informer.send_error_notification(
    error_title="数据记录失败",
    error_message="记录器运行时发生错误",
    error_details="详细的错误信息..."
)
```

### 状态管理

#### OneStateService

单状态服务，将所有状态保存在一个对象中。

```python
from zvt_data_recorder.core import OneStateService

class MyService(OneStateService):
    state_schema = MyStateSchema
    
    def __init__(self):
        super().__init__()
        if self.state is None:
            self.state = {'count': 0, 'last_update': None}
    
    def update_count(self):
        self.state['count'] += 1
        self.state['last_update'] = pd.Timestamp.now()
        self.persist_state()
```

#### EntityStateService

实体状态服务，为每个实体保存一个状态。

```python
from zvt_data_recorder.core import EntityStateService

class MyEntityService(EntityStateService):
    state_schema = MyStateSchema
    
    def __init__(self, entity_ids):
        super().__init__(entity_ids)
    
    def update_entity_state(self, entity_id, data):
        self.set_state(entity_id, {'data': data, 'updated': True})
        self.persist_state(entity_id)
```

### 类型定义

#### 枚举类型

```python
from zvt_data_recorder.types import (
    IntervalLevel, AdjustType, TradableType, Exchange
)

# 时间间隔
level = IntervalLevel.LEVEL_1DAY

# 复权类型
adjust = AdjustType.qfq

# 可交易类型
tradable_type = TradableType.stock

# 交易所
exchange = Exchange.sh
```

## 使用模式

### 基本使用模式

1. 定义数据模型
2. 定义记录器
3. 注册模式和记录器
4. 运行记录器
5. 查询数据

### 高级使用模式

1. 自定义状态管理
2. 批量数据处理
3. 数据验证和清洗
4. 错误处理和通知
5. 多数据源集成

## 最佳实践

1. **数据模型设计**: 合理设计表结构和索引
2. **记录器实现**: 实现幂等性和错误处理
3. **状态管理**: 使用状态服务跟踪记录进度
4. **配置管理**: 使用环境变量管理敏感配置
5. **日志记录**: 合理使用日志级别
6. **错误处理**: 实现重试机制和通知
7. **性能优化**: 批量插入和合理的休眠时间
