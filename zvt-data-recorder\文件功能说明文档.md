# ZVT-Data-Recorder 项目文件功能说明文档

## 📁 项目结构概览

```
zvt-data-recorder/
├── 📂 src/zvt_data_recorder/          # 核心框架代码
│   ├── 📂 config/                     # 配置管理模块
│   ├── 📂 core/                       # 核心功能模块
│   ├── 📂 database/                   # 数据库管理模块
│   ├── 📂 notifier/                   # 通知模块
│   ├── 📂 recorders/                  # 数据记录器模块
│   ├── 📂 types/                      # 类型定义模块
│   └── 📂 utils/                      # 工具模块
├── 📂 examples/                       # 示例代码
├── 📂 tests/                          # 测试代码
├── 📂 docs/                           # 文档
├── 📂 kline_data/                     # K线数据存储目录
├── 🐍 可执行脚本                      # 用户直接运行的脚本
├── ⚙️ 配置文件                        # 用户可修改的配置
├── 📊 CSV数据文件                     # 示例数据
└── 📖 文档文件                        # 说明文档
```

## 🗂️ 文件分类说明

### 🐍 可执行脚本（用户直接运行）

#### 1. **demo_run.py** - 项目演示脚本
- **功能**: 展示框架核心功能的完整演示
- **运行命令**: `python demo_run.py`
- **特点**: 
  - 无外部API依赖，纯本地演示
  - 展示数据验证、记录器、性能监控等功能
  - 生成模拟股票数据进行测试
- **适用场景**: 新用户了解项目功能，验证环境配置

#### 2. **start_kline_recorder.py** - QMT K线数据记录器
- **功能**: 启动QMT数据源的K线数据记录器
- **运行命令**: `python start_kline_recorder.py`
- **依赖**: 需要QMT客户端和配置文件
- **支持周期**: 日线、1小时、30分钟、15分钟、5分钟、1分钟
- **适用场景**: 实时获取和存储股票K线数据

#### 3. **import_csv_data.py** - CSV数据导入脚本
- **功能**: 将CSV格式的股票数据导入到数据库
- **运行命令**: `python import_csv_data.py`
- **支持格式**: GBK编码的中文CSV文件
- **处理能力**: 自动处理股票代码、字段映射、数据验证
- **适用场景**: 历史数据导入，数据迁移

#### 4. **verify_imported_data.py** - 数据验证脚本
- **功能**: 验证导入数据的完整性和准确性
- **运行命令**: `python verify_imported_data.py`
- **验证内容**: 数据完整性、价格逻辑、与原始文件对比
- **适用场景**: 数据导入后的质量检查

#### 5. **view_data.py** - 数据查看脚本
- **功能**: 查看和分析数据库中的数据
- **运行命令**: `python view_data.py`
- **功能**: 数据统计、图表展示、数据导出
- **适用场景**: 数据分析和可视化

#### 6. **check_database.py** - 数据库检查脚本
- **功能**: 检查数据库连接和表结构
- **运行命令**: `python check_database.py`
- **检查内容**: 数据库文件、表结构、数据统计
- **适用场景**: 数据库状态诊断

#### 7. **simple_example.py** - 简单示例脚本
- **功能**: 最基础的使用示例
- **运行命令**: `python simple_example.py`
- **特点**: 代码简洁，易于理解
- **适用场景**: 快速入门和概念验证

### 🖥️ 命令行工具

#### **src/zvt_data_recorder/cli.py** - 命令行接口
- **功能**: 提供完整的命令行管理工具
- **使用方式**: `python -m zvt_data_recorder.cli [命令] [参数]`
- **主要命令**:
  - `info`: 显示系统信息和配置状态
  - `list-recorders`: 列出可用的数据记录器
  - `run-recorder`: 运行指定的数据记录器
  - `db-status`: 检查数据库状态
  - `db-cleanup`: 清理数据库连接
  - `validate`: 验证系统配置和注册状态
- **适用场景**: 系统管理、自动化脚本、运维监控

### 🧪 测试脚本（开发和调试用）

#### 1. **tests/test_basic_functionality.py** - 基础功能测试
- **功能**: 测试核心功能的正确性
- **运行命令**: `python -m pytest tests/test_basic_functionality.py`
- **测试内容**: 数据模型、记录器、数据库操作
- **适用场景**: 开发过程中的功能验证

#### 2. **tests/test_enhanced_features.py** - 增强功能测试
- **功能**: 测试高级功能和边界情况
- **运行命令**: `python -m pytest tests/test_enhanced_features.py`
- **测试内容**: 性能测试、错误处理、并发操作
- **适用场景**: 系统稳定性验证

### ⚙️ 配置文件（用户可修改）

#### 1. **config_qmt.json** - QMT配置文件
- **功能**: QMT数据源的详细配置
- **关键配置项**:
  ```json
  {
    "qmt": {
      "client_path": "D:\\QMT\\userdata_mini",  # QMT客户端路径
      "account_id": "",                         # 账户ID
      "timeout": 30                             # 超时时间
    },
    "database": {
      "data_path": "./kline_data",              # 数据存储路径
      "db_name": "stock_kline_qmt"              # 数据库名称
    }
  }
  ```
- **修改建议**: 根据实际QMT安装路径和需求调整

#### 2. **requirements.txt** - Python依赖配置
- **功能**: 项目所需的Python包列表
- **安装命令**: `pip install -r requirements.txt`
- **主要依赖**: SQLAlchemy, pandas, numpy等

#### 3. **pyproject.toml** - 项目配置文件
- **功能**: 项目元数据和构建配置
- **包含**: 版本信息、依赖关系、构建设置

### 📊 CSV数据文件（示例数据）

#### 1. **sz002436.csv** - 兴森科技股票数据
- **功能**: 示例股票K线数据
- **格式**: GBK编码，包含OHLCV数据
- **记录数**: 3,492条（2010-2025年）
- **用途**: CSV导入功能测试

#### 2. **sz002435.csv** - 长江润发股票数据
- **功能**: 示例股票K线数据
- **格式**: GBK编码，包含OHLCV数据
- **记录数**: 3,247条（2010-2024年）
- **用途**: CSV导入功能测试

### 📂 核心框架代码（通常不需要用户修改）

#### src/zvt_data_recorder/config/
- **settings.py**: 配置管理核心类
  - 默认配置定义
  - 环境变量加载
  - 数据路径配置（已修改为 D:\MyData\zvt-data）

#### src/zvt_data_recorder/core/
- **recorder.py**: 数据记录器基类
  - 抽象记录器接口
  - 性能监控和统计
  - 错误处理和重试机制
- **schema.py**: 数据模型基类
  - Mixin基础类
  - TradableEntity可交易实体类
  - 数据验证机制
- **context.py**: 全局上下文管理
- **service.py**: 服务管理基类

#### src/zvt_data_recorder/database/
- **engine.py**: 数据库引擎管理
  - 数据库连接创建
  - 路径配置和文件管理
- **session.py**: 数据库会话管理
- **register.py**: 数据模式注册

#### src/zvt_data_recorder/recorders/
- **qmt_recorder.py**: QMT数据记录器实现
  - K线数据获取
  - 多周期数据支持
  - 实时数据更新

### 🛠️ 工具模块（可被其他脚本调用）

#### src/zvt_data_recorder/utils/
- **csv_importer.py**: CSV导入工具
  - CSVImporter基类
  - StockKlineCSVImporter股票K线导入器
  - 字段映射和数据转换
- **data_utils.py**: 数据处理工具
- **time_utils.py**: 时间处理工具
- **str_utils.py**: 字符串处理工具
- **recorder_utils.py**: 记录器工具函数

### 📖 示例和文档

#### examples/
- **basic_usage.py**: 基础使用示例
- **advanced_usage.py**: 高级功能示例
- **qmt_kdata_example.py**: QMT数据获取示例

#### docs/
- **API.md**: API文档
- **README.md**: 项目说明
- **README_QMT.md**: QMT功能说明

#### 生成的说明文档
- **CSV导入功能使用说明.md**: CSV导入详细说明
- **数据库路径配置说明.md**: 路径配置详细说明
- **文件功能说明文档.md**: 本文档
- **PROJECT_SUMMARY.md**: 项目总结文档
- **项目说明_无API设计.md**: 无API依赖设计说明
- **项目运行成功报告.md**: 项目运行状态报告
- **紧急修复完成报告.md**: 问题修复记录

### 🔧 开发和构建文件

#### 1. **setup.py** - 项目安装脚本
- **功能**: Python包的安装和分发配置
- **使用**: `pip install -e .` (开发模式安装)
- **包含**: 项目元数据、依赖关系、入口点

#### 2. **MANIFEST.in** - 包文件清单
- **功能**: 指定打包时包含的文件
- **作用**: 确保文档、配置文件等被正确打包

#### 3. **LICENSE** - 开源许可证
- **功能**: 项目的法律许可条款
- **重要性**: 明确使用权限和责任

#### 4. **CHANGELOG.md** - 版本更新日志
- **功能**: 记录项目的版本变更历史
- **格式**: 按版本组织的功能更新和bug修复记录

### 📁 数据存储目录

#### kline_data/
- **qmt/**: QMT数据源的数据文件
- **stock/**: 股票数据按周期分类存储
  - stock_day/: 日线数据
  - stock_1h/: 1小时数据
  - stock_30m/: 30分钟数据
  - stock_15m/: 15分钟数据
  - stock_5m/: 5分钟数据
  - stock_1m/: 1分钟数据

## 🚀 使用指南

### 新用户入门流程

1. **环境准备**
   ```bash
   pip install -r requirements.txt
   ```

2. **功能演示**
   ```bash
   python demo_run.py
   ```

3. **数据导入测试**
   ```bash
   python import_csv_data.py
   python verify_imported_data.py
   ```

4. **数据查看**
   ```bash
   python view_data.py
   ```

### 常见使用场景

#### 场景1: CSV历史数据导入
1. 准备CSV文件（GBK编码）
2. 运行 `python import_csv_data.py`
3. 验证 `python verify_imported_data.py`

#### 场景2: QMT实时数据记录
1. 配置 `config_qmt.json`
2. 启动QMT客户端
3. 运行 `python start_kline_recorder.py`

#### 场景3: 自定义数据记录器开发
1. 参考 `examples/basic_usage.py`
2. 继承 `Recorder` 基类
3. 实现数据获取和处理逻辑

### 定制化配置建议

#### 必须配置的文件
1. **数据存储路径**: 已配置为 `D:\MyData\zvt-data`
2. **QMT路径**: 修改 `config_qmt.json` 中的 `client_path`
3. **数据库配置**: 根据需要调整数据库类型和参数

#### 可选配置的文件
1. **日志级别**: 在各脚本中调整 `LOG_LEVEL`
2. **性能参数**: 调整批处理大小、超时时间等
3. **通知配置**: 配置邮件通知功能

### 🎯 文件优先级和学习路径

#### 🥇 第一优先级（必须了解）
1. **demo_run.py** - 了解项目整体功能
2. **README.md** - 项目基本介绍
3. **requirements.txt** - 环境依赖

#### 🥈 第二优先级（核心功能）
1. **import_csv_data.py** - 数据导入功能
2. **start_kline_recorder.py** - 实时数据记录
3. **config_qmt.json** - 核心配置

#### 🥉 第三优先级（深入理解）
1. **src/zvt_data_recorder/core/** - 框架核心
2. **examples/** - 使用示例
3. **tests/** - 测试用例

#### 📚 参考优先级（按需查阅）
1. **docs/API.md** - API详细文档
2. **各种说明文档** - 特定功能说明
3. **工具模块** - 开发时参考

### ⚠️ 重要注意事项

#### 不建议直接修改的文件
- `src/zvt_data_recorder/` 下的核心框架代码
- `__pycache__/` 目录下的缓存文件
- 自动生成的数据库文件

#### 建议备份的文件
- 配置文件（`config_qmt.json` 等）
- 自定义脚本和数据文件
- 重要的数据库文件

#### 版本控制建议
- 包含：源代码、配置文件、文档
- 排除：`__pycache__/`、数据文件、日志文件

## 📋 文件依赖关系图

```
demo_run.py → core/recorder.py → database/session.py → database/engine.py → config/settings.py
import_csv_data.py → utils/csv_importer.py → core/schema.py → database/register.py
start_kline_recorder.py → recorders/qmt_recorder.py → config_qmt.json
verify_imported_data.py → import_csv_data.py (数据模型)
```

## 📋 快速参考表

| 需求场景 | 推荐文件 | 运行命令 | 说明 |
|---------|---------|----------|------|
| 🎯 **快速体验** | `demo_run.py` | `python demo_run.py` | 无依赖演示 |
| 📊 **导入CSV数据** | `import_csv_data.py` | `python import_csv_data.py` | 历史数据导入 |
| 📈 **实时数据记录** | `start_kline_recorder.py` | `python start_kline_recorder.py` | 需要QMT |
| 🔍 **数据验证** | `verify_imported_data.py` | `python verify_imported_data.py` | 数据质量检查 |
| 👀 **查看数据** | `view_data.py` | `python view_data.py` | 数据分析 |
| 🔧 **系统诊断** | `check_database.py` | `python check_database.py` | 数据库检查 |
| 🖥️ **命令行管理** | `cli.py` | `python -m zvt_data_recorder.cli info` | 系统管理 |
| 📚 **学习示例** | `examples/basic_usage.py` | `python examples/basic_usage.py` | 基础教程 |
| 🧪 **功能测试** | `tests/test_*.py` | `python -m pytest tests/` | 单元测试 |

## 🎯 常见问题快速解决

| 问题类型 | 查看文件 | 解决方案 |
|---------|---------|----------|
| 🔧 **配置路径** | `数据库路径配置说明.md` | 修改存储路径 |
| 📊 **CSV导入** | `CSV导入功能使用说明.md` | 导入格式和步骤 |
| ⚙️ **QMT配置** | `config_qmt.json` + `README_QMT.md` | QMT客户端设置 |
| 🐛 **运行错误** | `项目运行成功报告.md` | 常见问题解决 |
| 📖 **API使用** | `docs/API.md` | 详细API文档 |

## 💡 最佳实践建议

1. **开发环境**: 先运行 `demo_run.py` 验证环境
2. **数据导入**: 使用 `import_csv_data.py` 导入历史数据
3. **实时数据**: 配置QMT后使用 `start_kline_recorder.py`
4. **数据验证**: 定期运行验证脚本确保数据质量
5. **自定义开发**: 基于 `examples/` 中的示例进行扩展
6. **系统监控**: 使用CLI工具进行系统状态检查
7. **问题排查**: 查看对应的说明文档和日志文件
