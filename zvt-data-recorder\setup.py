#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ZVT Data Recorder Setup Script

独立的数据记录库，从ZVT项目中提取的数据记录功能。
"""

from codecs import open
from os import path
from setuptools import setup, find_packages

here = path.abspath(path.dirname(__file__))

# 获取长描述
try:
    with open(path.join(here, "README.md"), encoding="utf-8") as f:
        long_description = f.read()
except:
    long_description = "ZVT Data Recorder - 独立的数据记录库"

# 获取版本信息
version = {}
try:
    with open(path.join(here, "src", "zvt_data_recorder", "__init__.py"), encoding="utf-8") as f:
        exec(f.read(), version)
except:
    version = {"__version__": "1.0.0"}

setup(
    name="zvt-data-recorder",
    version=version.get("__version__", "1.0.0"),
    description="独立的数据记录库，提供完整的数据记录、存储和管理能力",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/zvt-data-recorder",
    author="ZVT Data Recorder Team",
    author_email="<EMAIL>",
    license="MIT",
    
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: OS Independent",
    ],
    
    keywords="data recorder database finance quant time-series sqlalchemy pandas",
    
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    
    python_requires=">=3.9",
    
    install_requires=[
        "requests>=2.31.0",
        "SQLAlchemy>=2.0.0,<3.0.0", 
        "pandas>=2.0.0",
        "arrow>=1.2.0",
        "numpy>=1.24.0",
        "openpyxl>=3.1.0",
        "pytz>=2023.3",
        "pydantic>=2.0.0",
    ],
    
    extras_require={
        "mysql": ["PyMySQL>=1.0.0"],
        "postgresql": ["psycopg2-binary>=2.9.0"],
        "dev": [
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0", 
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "docs": [
            "sphinx>=6.0.0",
            "sphinx-rtd-theme>=1.2.0",
        ],
        "all": [
            "PyMySQL>=1.0.0",
            "psycopg2-binary>=2.9.0",
            "pytest>=7.0.0",
            "pytest-cov>=4.0.0",
            "black>=23.0.0", 
            "flake8>=6.0.0",
            "mypy>=1.0.0",
            "sphinx>=6.0.0",
            "sphinx-rtd-theme>=1.2.0",
        ],
    },
    
    include_package_data=True,
    
    entry_points={
        "console_scripts": [
            "zvt-data-recorder=zvt_data_recorder.cli:main",
        ],
    },
    
    project_urls={
        "Bug Reports": "https://github.com/your-username/zvt-data-recorder/issues",
        "Source": "https://github.com/your-username/zvt-data-recorder",
        "Documentation": "https://zvt-data-recorder.readthedocs.io/",
    },
    
    zip_safe=False,
)
