# -*- coding: utf-8 -*-
"""
时间处理工具

从ZVT项目中提取的时间处理相关工具函数。
"""

import calendar
import datetime

import arrow
import pandas as pd

CHINA_TZ = "Asia/Shanghai"

TIME_FORMAT_ISO8601 = "YYYY-MM-DDTHH:mm:ss.SSS"
TIME_FORMAT_MON = "YYYY-MM"
TIME_FORMAT_DAY = "YYYY-MM-DD"
TIME_FORMAT_DAY1 = "YYYYMMDD"
TIME_FORMAT_MINUTE = "YYYYMMDDHHmm"
TIME_FORMAT_SECOND = "YYYYMMDDHHmmss"
TIME_FORMAT_MINUTE1 = "HH:mm"
TIME_FORMAT_MINUTE2 = "YYYY-MM-DD HH:mm:ss"


def to_pd_timestamp(the_time) -> pd.Timestamp:
    """
    将时间转换为pandas时间戳
    
    :param the_time: 时间，可以是毫秒(int)、秒(float)或字符串
    :return: pandas时间戳
    """
    if the_time is None:
        return None
    if type(the_time) == int:
        return pd.Timestamp.fromtimestamp(the_time / 1000)
    if type(the_time) == float:
        return pd.Timestamp.fromtimestamp(the_time)
    return pd.Timestamp(the_time)


def to_time_str(timestamp, fmt=TIME_FORMAT_DAY):
    """
    将时间戳转换为字符串
    
    :param timestamp: 时间戳
    :param fmt: 格式字符串
    :return: 时间字符串
    """
    if timestamp is None:
        return None
    
    if isinstance(timestamp, pd.Timestamp):
        return timestamp.strftime(fmt.replace("YYYY", "%Y").replace("MM", "%m").replace("DD", "%d")
                                 .replace("HH", "%H").replace("mm", "%M").replace("ss", "%S"))
    
    # 使用arrow处理其他格式
    try:
        return arrow.get(timestamp).format(fmt)
    except:
        return str(timestamp)


def now_pd_timestamp():
    """获取当前时间的pandas时间戳"""
    return pd.Timestamp.now()


def now_time_str(fmt=TIME_FORMAT_DAY):
    """获取当前时间的字符串表示"""
    return to_time_str(now_pd_timestamp(), fmt)


def date_and_time(the_date, the_time):
    """
    合并日期和时间
    
    :param the_date: 日期
    :param the_time: 时间
    :return: 合并后的时间戳
    """
    if isinstance(the_date, str):
        the_date = to_pd_timestamp(the_date)
    if isinstance(the_time, str):
        the_time = to_pd_timestamp(the_time)
    
    return pd.Timestamp.combine(the_date.date(), the_time.time())


def is_same_time(time1, time2):
    """
    判断两个时间是否相同
    
    :param time1: 时间1
    :param time2: 时间2
    :return: 是否相同
    """
    if time1 is None and time2 is None:
        return True
    if time1 is None or time2 is None:
        return False
    
    ts1 = to_pd_timestamp(time1)
    ts2 = to_pd_timestamp(time2)
    
    return ts1 == ts2


def current_date():
    """获取当前日期"""
    return now_pd_timestamp().date()


def date_time_by_interval(start_date, interval_days):
    """
    根据间隔计算日期
    
    :param start_date: 开始日期
    :param interval_days: 间隔天数
    :return: 计算后的日期
    """
    start_ts = to_pd_timestamp(start_date)
    return start_ts + pd.Timedelta(days=interval_days)


def get_year_quarter(timestamp):
    """
    获取时间戳的年份和季度
    
    :param timestamp: 时间戳
    :return: (年份, 季度)
    """
    ts = to_pd_timestamp(timestamp)
    return ts.year, ts.quarter


def get_year_month(timestamp):
    """
    获取时间戳的年份和月份
    
    :param timestamp: 时间戳
    :return: (年份, 月份)
    """
    ts = to_pd_timestamp(timestamp)
    return ts.year, ts.month


def get_year_week(timestamp):
    """
    获取时间戳的年份和周数
    
    :param timestamp: 时间戳
    :return: (年份, 周数)
    """
    ts = to_pd_timestamp(timestamp)
    return ts.year, ts.week


def is_trading_day(timestamp):
    """
    判断是否为交易日（简单实现，不考虑节假日）
    
    :param timestamp: 时间戳
    :return: 是否为交易日
    """
    ts = to_pd_timestamp(timestamp)
    # 周一到周五为交易日
    return ts.weekday() < 5


def next_trading_day(timestamp):
    """
    获取下一个交易日
    
    :param timestamp: 时间戳
    :return: 下一个交易日
    """
    ts = to_pd_timestamp(timestamp)
    next_day = ts + pd.Timedelta(days=1)
    
    # 如果是周末，跳到下周一
    while next_day.weekday() >= 5:
        next_day += pd.Timedelta(days=1)
    
    return next_day


def prev_trading_day(timestamp):
    """
    获取上一个交易日
    
    :param timestamp: 时间戳
    :return: 上一个交易日
    """
    ts = to_pd_timestamp(timestamp)
    prev_day = ts - pd.Timedelta(days=1)
    
    # 如果是周末，跳到上周五
    while prev_day.weekday() >= 5:
        prev_day -= pd.Timedelta(days=1)
    
    return prev_day


__all__ = [
    # 常量
    "CHINA_TZ",
    "TIME_FORMAT_ISO8601",
    "TIME_FORMAT_MON", 
    "TIME_FORMAT_DAY",
    "TIME_FORMAT_DAY1",
    "TIME_FORMAT_MINUTE",
    "TIME_FORMAT_SECOND",
    "TIME_FORMAT_MINUTE1",
    "TIME_FORMAT_MINUTE2",
    
    # 时间转换函数
    "to_pd_timestamp",
    "to_time_str",
    "now_pd_timestamp", 
    "now_time_str",
    "date_and_time",
    "is_same_time",
    "current_date",
    "date_time_by_interval",
    
    # 时间分析函数
    "get_year_quarter",
    "get_year_month", 
    "get_year_week",
    "is_trading_day",
    "next_trading_day",
    "prev_trading_day",
]
