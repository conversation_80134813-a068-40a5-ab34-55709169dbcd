# -*- coding: utf-8 -*-
"""
记录器工具函数

从ZVT项目中提取的记录器相关工具函数。
"""

import logging
import time
from typing import Type, List, Any

from ..core.schema import Mixin
from ..config.settings import get_config

logger = logging.getLogger(__name__)


def run_data_recorder(
    domain: Type[Mixin],
    entity_provider: str = None,
    data_provider: str = None,
    entity_ids: List[str] = None,
    retry_times: int = 10,
    sleeping_time: int = 10,
    return_unfinished: bool = False,
    **recorder_kv
):
    """
    运行数据记录器
    
    :param domain: 数据模式类
    :param entity_provider: 实体提供者
    :param data_provider: 数据提供者
    :param entity_ids: 实体ID列表
    :param retry_times: 重试次数
    :param sleeping_time: 休眠时间
    :param return_unfinished: 是否返回未完成的实体ID
    :param recorder_kv: 记录器参数
    :return: 未完成的实体ID列表（如果return_unfinished为True）
    """
    logger.info(f"Record data: {domain.__name__}, entity_provider: {entity_provider}, data_provider: {data_provider}")
    
    unfinished_entity_ids = entity_ids
    
    # 获取记录器类
    if not hasattr(domain, 'provider_map_recorder') or data_provider not in domain.provider_map_recorder:
        logger.error(f"No recorder found for provider {data_provider} in {domain.__name__}")
        return unfinished_entity_ids if return_unfinished else None
    
    recorder_cls = domain.provider_map_recorder[data_provider]
    
    # 重试机制
    for i in range(retry_times):
        try:
            logger.info(f"Try {i + 1}/{retry_times} to record {domain.__name__}")
            
            # 创建记录器实例
            recorder_kwargs = {
                'entity_ids': unfinished_entity_ids,
                'sleeping_time': sleeping_time,
                'return_unfinished': return_unfinished,
                **recorder_kv
            }

            # 只有当记录器支持entity_provider参数时才添加
            import inspect
            sig = inspect.signature(recorder_cls.__init__)
            if 'entity_provider' in sig.parameters and entity_provider is not None:
                recorder_kwargs['entity_provider'] = entity_provider
            
            recorder = recorder_cls(**recorder_kwargs)
            
            # 运行记录器
            if return_unfinished:
                unfinished_entity_ids = recorder.run()
                if not unfinished_entity_ids:
                    logger.info(f"Successfully recorded all data for {domain.__name__}")
                    break
                else:
                    logger.info(f"Still have {len(unfinished_entity_ids)} unfinished entities")
            else:
                recorder.run()
                logger.info(f"Successfully recorded data for {domain.__name__}")
                break
                
        except Exception as e:
            logger.error(f"Failed to record data for {domain.__name__}: {e}")
            if i == retry_times - 1:
                logger.error(f"Failed to record data after {retry_times} retries")
                # 可以在这里发送通知
                _send_error_notification(domain, e)
            else:
                logger.info(f"Will retry after {sleeping_time} seconds")
                time.sleep(sleeping_time)
    
    return unfinished_entity_ids if return_unfinished else None


def _send_error_notification(domain: Type[Mixin], error: Exception):
    """
    发送错误通知
    
    :param domain: 数据模式类
    :param error: 错误信息
    """
    try:
        config = get_config()
        if config.EMAIL_ENABLED:
            from ..notifier.email import EmailInformer
            
            email_informer = EmailInformer()
            subject = f"数据记录失败: {domain.__name__}"
            message = f"数据记录器运行失败:\n\n域: {domain.__name__}\n错误: {str(error)}"
            
            email_informer.send_message(subject, message)
    except Exception as e:
        logger.error(f"Failed to send error notification: {e}")


def validate_recorder_config(recorder_cls: Type, **config) -> bool:
    """
    验证记录器配置
    
    :param recorder_cls: 记录器类
    :param config: 配置参数
    :return: 是否有效
    """
    try:
        # 检查必需的属性
        required_attrs = ['provider', 'data_schema']
        for attr in required_attrs:
            if not hasattr(recorder_cls, attr) or getattr(recorder_cls, attr) is None:
                logger.error(f"Recorder {recorder_cls.__name__} missing required attribute: {attr}")
                return False
        
        # 检查数据模式是否注册了提供者
        data_schema = recorder_cls.data_schema
        provider = recorder_cls.provider
        
        if hasattr(data_schema, 'providers') and provider not in data_schema.providers:
            logger.error(f"Provider {provider} not registered for schema {data_schema.__name__}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Failed to validate recorder config: {e}")
        return False


def get_recorder_status(recorder_cls: Type) -> dict:
    """
    获取记录器状态信息
    
    :param recorder_cls: 记录器类
    :return: 状态信息
    """
    status = {
        'class_name': recorder_cls.__name__,
        'provider': getattr(recorder_cls, 'provider', None),
        'data_schema': getattr(recorder_cls, 'data_schema', None),
        'entity_provider': getattr(recorder_cls, 'entity_provider', None),
        'entity_schema': getattr(recorder_cls, 'entity_schema', None),
        'valid': False,
        'issues': []
    }
    
    # 检查配置
    if validate_recorder_config(recorder_cls):
        status['valid'] = True
    else:
        status['issues'].append('Invalid configuration')
    
    # 添加数据模式信息
    if status['data_schema']:
        schema_cls = status['data_schema']
        status['data_schema_name'] = schema_cls.__name__
        status['table_name'] = getattr(schema_cls, '__tablename__', None)
        status['providers'] = getattr(schema_cls, 'providers', [])
    
    return status


def list_available_recorders() -> List[dict]:
    """
    列出所有可用的记录器
    
    :return: 记录器信息列表
    """
    from ..core.context import get_context
    
    recorders = []
    context = get_context()
    
    # 遍历所有注册的模式
    for schema in context.schemas:
        if hasattr(schema, 'provider_map_recorder'):
            for provider, recorder_cls in schema.provider_map_recorder.items():
                recorder_info = get_recorder_status(recorder_cls)
                recorder_info['schema_name'] = schema.__name__
                recorders.append(recorder_info)
    
    return recorders


def create_recorder_instance(
    domain: Type[Mixin],
    provider: str,
    **kwargs
):
    """
    创建记录器实例
    
    :param domain: 数据模式类
    :param provider: 数据提供者
    :param kwargs: 记录器参数
    :return: 记录器实例
    """
    if not hasattr(domain, 'provider_map_recorder') or provider not in domain.provider_map_recorder:
        raise ValueError(f"No recorder found for provider {provider} in {domain.__name__}")
    
    recorder_cls = domain.provider_map_recorder[provider]
    
    # 验证配置
    if not validate_recorder_config(recorder_cls, **kwargs):
        raise ValueError(f"Invalid recorder configuration for {recorder_cls.__name__}")
    
    return recorder_cls(**kwargs)


def estimate_recording_time(
    domain: Type[Mixin],
    entity_count: int,
    sleeping_time: int = 10
) -> dict:
    """
    估算记录时间
    
    :param domain: 数据模式类
    :param entity_count: 实体数量
    :param sleeping_time: 休眠时间
    :return: 时间估算信息
    """
    # 基础估算（每个实体平均处理时间）
    base_time_per_entity = 2  # 秒
    
    # 总处理时间
    processing_time = entity_count * base_time_per_entity
    
    # 休眠时间
    sleep_time = (entity_count - 1) * sleeping_time if entity_count > 1 else 0
    
    # 总时间
    total_time = processing_time + sleep_time
    
    return {
        'entity_count': entity_count,
        'processing_time_seconds': processing_time,
        'sleep_time_seconds': sleep_time,
        'total_time_seconds': total_time,
        'estimated_duration': f"{total_time // 3600}h {(total_time % 3600) // 60}m {total_time % 60}s",
        'sleeping_time': sleeping_time
    }


def batch_record_data(
    domains: List[Type[Mixin]],
    provider: str,
    entity_ids: List[str] = None,
    **kwargs
) -> dict:
    """
    批量记录数据
    
    :param domains: 数据模式类列表
    :param provider: 数据提供者
    :param entity_ids: 实体ID列表
    :param kwargs: 记录器参数
    :return: 记录结果
    """
    results = {
        'success': [],
        'failed': [],
        'total': len(domains),
        'start_time': time.time()
    }
    
    for domain in domains:
        try:
            logger.info(f"Recording data for {domain.__name__}")
            
            run_data_recorder(
                domain=domain,
                data_provider=provider,
                entity_ids=entity_ids,
                **kwargs
            )
            
            results['success'].append(domain.__name__)
            logger.info(f"Successfully recorded data for {domain.__name__}")
            
        except Exception as e:
            logger.error(f"Failed to record data for {domain.__name__}: {e}")
            results['failed'].append({
                'domain': domain.__name__,
                'error': str(e)
            })
    
    results['end_time'] = time.time()
    results['duration'] = results['end_time'] - results['start_time']
    results['success_count'] = len(results['success'])
    results['failed_count'] = len(results['failed'])
    
    logger.info(f"Batch recording completed: {results['success_count']} success, {results['failed_count']} failed")
    
    return results


__all__ = [
    "run_data_recorder",
    "validate_recorder_config",
    "get_recorder_status",
    "list_available_recorders",
    "create_recorder_instance",
    "estimate_recording_time",
    "batch_record_data",
]
