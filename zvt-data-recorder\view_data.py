#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
查看保存的K线数据
"""

import sqlite3
import pandas as pd
import os

def view_kline_data(db_path, table_name, limit=10):
    """查看K线数据"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 查询数据
        query = f"""
        SELECT * FROM {table_name} 
        ORDER BY timestamp DESC 
        LIMIT {limit}
        """
        
        df = pd.read_sql_query(query, conn)
        
        if df.empty:
            print(f"❌ 表 {table_name} 中没有数据")
        else:
            print(f"✅ 表 {table_name} 中的最新 {len(df)} 条记录:")
            print("=" * 80)
            
            # 显示主要字段
            display_columns = []
            for col in ['timestamp', 'entity_id', 'open', 'high', 'low', 'close', 'volume', 'turnover']:
                if col in df.columns:
                    display_columns.append(col)
            
            print(df[display_columns].to_string(index=False))
            
            print(f"\n📊 数据统计:")
            print(f"- 总记录数: {len(df)}")
            print(f"- 时间范围: {df['timestamp'].min()} ~ {df['timestamp'].max()}")
            if 'close' in df.columns:
                print(f"- 价格范围: {df['close'].min():.2f} ~ {df['close'].max():.2f}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """主函数"""
    print("🔍 K线数据查看工具")
    print("=" * 50)
    
    # 查看QMT数据库中的日K线数据
    db_path = "kline_data/qmt/qmt_stock_kline_qmt.db"
    
    print("\n📊 查看股票基本信息:")
    view_kline_data(db_path, "stock", 10)
    
    print("\n📈 查看日K线数据:")
    view_kline_data(db_path, "stock_kline_day", 10)

if __name__ == '__main__':
    main()
