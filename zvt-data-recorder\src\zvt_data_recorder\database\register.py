# -*- coding: utf-8 -*-
"""
数据库注册机制

从ZVT项目中提取的数据库和模式注册功能。
"""

import logging
from typing import List, Type

from sqlalchemy.ext.declarative import DeclarativeMeta

from ..core.context import get_context
from ..core.schema import TradableEntity

logger = logging.getLogger(__name__)


def register_entity(entity_type: str = None):
    """
    注册实体类型的装饰器
    
    :param entity_type: 实体类型名称
    :return: 装饰器函数
    """
    
    def register(cls):
        # 注册实体
        if issubclass(cls, TradableEntity):
            entity_type_ = entity_type
            if not entity_type:
                entity_type_ = cls.__name__.lower()
            
            context = get_context()
            
            if entity_type_ not in context.tradable_entity_types:
                context.tradable_entity_types.append(entity_type_)
                context.tradable_entity_schemas.append(cls)
            
            context.tradable_schema_map[entity_type_] = cls
            
            logger.info(f"Registered entity type: {entity_type_} -> {cls.__name__}")
        
        return cls
    
    return register


def register_schema(
    providers: List[str],
    db_name: str,
    schema_base: DeclarativeMeta,
    entity_type: str = None,
):
    """
    注册数据模式
    
    :param providers: 数据提供者列表
    :param db_name: 数据库名称
    :param schema_base: 模式基类
    :param entity_type: 实体类型
    """
    context = get_context()
    
    # 注册数据库名称和基类的映射
    if db_name not in context.dbname_map_base:
        context.dbname_map_base[db_name] = []
    
    if schema_base not in context.dbname_map_base[db_name]:
        context.dbname_map_base[db_name].append(schema_base)
    
    # 为每个提供者注册
    for provider in providers:
        # 注册提供者
        context.register_provider(provider)
        
        # 建立provider -> db_name映射
        if provider not in context.provider_map_dbnames:
            context.provider_map_dbnames[provider] = []
        if db_name not in context.provider_map_dbnames[provider]:
            context.provider_map_dbnames[provider].append(db_name)
    
    # 如果指定了实体类型，建立实体类型与模式的映射
    if entity_type:
        if entity_type not in context.entity_map_schemas:
            context.entity_map_schemas[entity_type] = []
        
        # 这里暂时不添加具体的模式，因为模式类还没有创建
        # 实际的模式注册会在模式类创建时通过元类自动完成
    
    logger.info(f"Registered schema: providers={providers}, db_name={db_name}, entity_type={entity_type}")


def register_schema_to_context(schema_cls: Type, db_name: str):
    """
    将模式类注册到上下文
    
    :param schema_cls: 模式类
    :param db_name: 数据库名称
    """
    context = get_context()
    
    # 注册模式
    if schema_cls not in context.schemas:
        context.schemas.append(schema_cls)
    
    # 建立db_name -> schema映射
    if db_name not in context.dbname_map_schemas:
        context.dbname_map_schemas[db_name] = []
    
    if schema_cls not in context.dbname_map_schemas[db_name]:
        context.dbname_map_schemas[db_name].append(schema_cls)
    
    logger.debug(f"Registered schema to context: {schema_cls.__name__} -> {db_name}")


def get_providers() -> List[str]:
    """
    获取所有注册的提供者
    
    :return: 提供者列表
    """
    context = get_context()
    return context.get_providers()


def get_schemas(provider: str) -> List[DeclarativeMeta]:
    """
    获取提供者支持的数据模式
    
    :param provider: 数据提供者
    :return: 模式列表
    """
    context = get_context()
    return context.get_schemas_by_provider(provider)


def get_entity_schema(entity_type: str) -> Type[TradableEntity]:
    """
    根据实体类型获取实体模式
    
    :param entity_type: 实体类型，例如 stock, stockus
    :return: 实体模式类
    """
    context = get_context()
    return context.get_entity_schema(entity_type)


def get_schema_by_name(name: str) -> DeclarativeMeta:
    """
    根据名称获取模式
    
    :param name: 模式名称
    :return: 模式类
    """
    context = get_context()
    
    for schema in context.schemas:
        if schema.__name__ == name:
            return schema
    
    return None


def list_registered_entities():
    """
    列出所有注册的实体类型
    
    :return: 实体类型信息
    """
    context = get_context()
    
    entities_info = []
    for entity_type in context.tradable_entity_types:
        schema_cls = context.tradable_schema_map.get(entity_type)
        entities_info.append({
            'entity_type': entity_type,
            'schema_class': schema_cls.__name__ if schema_cls else None,
            'providers': schema_cls.get_providers() if schema_cls and hasattr(schema_cls, 'get_providers') else []
        })
    
    return entities_info


def list_registered_schemas():
    """
    列出所有注册的模式
    
    :return: 模式信息
    """
    context = get_context()
    
    schemas_info = []
    for db_name, schemas in context.dbname_map_schemas.items():
        for schema in schemas:
            providers = []
            # 查找使用此数据库的提供者
            for provider, db_names in context.provider_map_dbnames.items():
                if db_name in db_names:
                    providers.append(provider)
            
            schemas_info.append({
                'schema_class': schema.__name__,
                'db_name': db_name,
                'providers': providers,
                'table_name': getattr(schema, '__tablename__', None)
            })
    
    return schemas_info


def validate_registration():
    """
    验证注册信息的完整性
    
    :return: 验证结果
    """
    context = get_context()
    issues = []
    
    # 检查提供者是否有对应的数据库
    for provider in context.providers:
        if provider not in context.provider_map_dbnames:
            issues.append(f"Provider {provider} has no registered databases")
    
    # 检查数据库是否有对应的模式
    for db_name in context.dbname_map_base.keys():
        if db_name not in context.dbname_map_schemas:
            issues.append(f"Database {db_name} has no registered schemas")
    
    # 检查实体类型是否有对应的模式
    for entity_type in context.tradable_entity_types:
        if entity_type not in context.tradable_schema_map:
            issues.append(f"Entity type {entity_type} has no registered schema")
    
    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'summary': {
            'providers': len(context.providers),
            'entity_types': len(context.tradable_entity_types),
            'schemas': len(context.schemas),
            'databases': len(context.dbname_map_base)
        }
    }


__all__ = [
    "register_entity",
    "register_schema",
    "register_schema_to_context",
    "get_providers",
    "get_schemas",
    "get_entity_schema",
    "get_schema_by_name",
    "list_registered_entities",
    "list_registered_schemas",
    "validate_registration",
]
