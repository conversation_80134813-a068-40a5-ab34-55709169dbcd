# -*- coding: utf-8 -*-
"""
数据模型类型定义

从ZVT项目中提取的数据模型基础类。
"""


class Bean(object):
    """
    基础数据对象类
    """
    
    def __init__(self) -> None:
        super().__init__()
        self.__dict__

    def dict(self):
        """返回对象的字典表示"""
        return self.__dict__

    def from_dct(self, dct: dict):
        """从字典创建对象"""
        if dct:
            for k in dct:
                self.__dict__[k] = dct[k]


__all__ = ["Bean"]
