#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
QMT数据获取测试脚本
用于诊断QMT数据获取问题
"""

import sys
import os
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_qmt_connection():
    """测试QMT连接"""
    try:
        import xtquant.xtdata as xt
        print("📡 正在连接QMT...")

        result = xt.connect()
        print(f"连接结果类型: {type(result)}")

        # QMT连接成功会返回客户端对象，不是错误码
        if result is not None:
            print("✅ QMT连接成功")
            return xt
        else:
            print(f"❌ QMT连接失败")
            return None
            
    except ImportError:
        print("❌ 无法导入xtquant模块，请检查QMT安装")
        return None
    except Exception as e:
        print(f"❌ QMT连接异常: {e}")
        return None

def test_qmt_stock_list(xt):
    """测试获取股票列表"""
    try:
        print("\n📊 测试获取股票列表...")
        
        # 尝试不同的方法获取股票列表
        methods = [
            ('get_stock_list_in_sector', '沪深A股'),
            ('get_stock_list_in_sector', 'A股'),
            ('get_stock_list_in_sector', '全部A股'),
        ]
        
        for method_name, sector in methods:
            try:
                if hasattr(xt, method_name):
                    method = getattr(xt, method_name)
                    stocks = method(sector)
                    if stocks:
                        print(f"✅ {method_name}('{sector}') 返回 {len(stocks)} 只股票")
                        print(f"前10只股票: {stocks[:10]}")
                        return stocks[:20]  # 返回前20只用于测试
                    else:
                        print(f"⚠️ {method_name}('{sector}') 返回空列表")
                else:
                    print(f"⚠️ 方法 {method_name} 不存在")
            except Exception as e:
                print(f"❌ {method_name}('{sector}') 失败: {e}")
        
        # 如果都失败，使用固定的测试股票
        print("🔄 使用固定测试股票...")
        return ['600000.SH', '000001.SZ', '600519.SH']
        
    except Exception as e:
        print(f"❌ 获取股票列表失败: {e}")
        return ['600000.SH', '000001.SZ', '600519.SH']

def test_qmt_kline_data(xt, stock_codes):
    """测试获取K线数据"""
    print(f"\n📈 测试获取K线数据...")
    
    # 测试不同的时间范围
    today = datetime.now()
    test_ranges = [
        # 最近30天
        ((today - timedelta(days=30)).strftime('%Y%m%d'), today.strftime('%Y%m%d')),
        # 最近7天
        ((today - timedelta(days=7)).strftime('%Y%m%d'), today.strftime('%Y%m%d')),
        # 固定范围（2024年）
        ('20240701', '20240731'),
        # 更早的范围
        ('20240101', '20240131'),
    ]
    
    for stock_code in stock_codes[:3]:  # 只测试前3只股票
        print(f"\n🔍 测试股票: {stock_code}")
        
        for start_date, end_date in test_ranges:
            print(f"  时间范围: {start_date} ~ {end_date}")
            
            try:
                # 尝试不同的API方法
                methods = [
                    ('get_market_data_ex', {
                        'stock_list': [stock_code],
                        'period': '1d',
                        'start_time': start_date,
                        'end_time': end_date,
                        'count': -1,
                        'dividend_type': 'front',
                        'fill_data': True
                    }),
                    ('get_market_data_ex', {
                        'stock_list': [stock_code],
                        'period': '1d',
                        'start_time': start_date,
                        'end_time': end_date
                    }),
                    ('get_market_data', {
                        'stock_list': [stock_code],
                        'period': '1d',
                        'start_time': start_date,
                        'end_time': end_date
                    }),
                ]
                
                for method_name, params in methods:
                    if hasattr(xt, method_name):
                        try:
                            method = getattr(xt, method_name)
                            data = method(**params)
                            
                            print(f"    {method_name}: 返回类型 {type(data)}")
                            
                            if isinstance(data, dict):
                                print(f"    数据键: {list(data.keys())}")
                                for key, value in data.items():
                                    if hasattr(value, 'shape'):
                                        print(f"    {key}: DataFrame shape {value.shape}")
                                        if not value.empty:
                                            print(f"    {key}: 列名 {list(value.columns)}")
                                            print(f"    {key}: 前3行数据:")
                                            print(value.head(3))
                                            return True  # 找到有效数据就返回
                                    else:
                                        print(f"    {key}: {type(value)} - {value}")
                            else:
                                print(f"    数据: {data}")
                                
                        except Exception as e:
                            print(f"    {method_name} 失败: {e}")
                    else:
                        print(f"    方法 {method_name} 不存在")
                        
            except Exception as e:
                print(f"  测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🧪 QMT数据获取诊断测试")
    print("=" * 50)
    
    # 1. 测试连接
    xt = test_qmt_connection()
    if not xt:
        print("❌ QMT连接失败，无法继续测试")
        return
    
    # 2. 测试股票列表
    stock_codes = test_qmt_stock_list(xt)
    if not stock_codes:
        print("❌ 无法获取股票列表，无法继续测试")
        return
    
    # 3. 测试K线数据
    success = test_qmt_kline_data(xt, stock_codes)
    
    if success:
        print("\n✅ QMT数据获取测试成功")
    else:
        print("\n❌ QMT数据获取测试失败")
        print("可能的原因:")
        print("1. QMT客户端未启动或未登录")
        print("2. 选择的时间范围没有交易数据")
        print("3. QMT API版本不兼容")
        print("4. 网络连接问题")

if __name__ == "__main__":
    main()
