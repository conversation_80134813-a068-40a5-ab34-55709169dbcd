#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
一键启动K线数据记录器 - QMT数据源版本 (优化版)

这个脚本提供了一键启动全量K线数据记录的功能。
使用QMT作为唯一数据源，支持多个时间周期的K线数据记录。

新增功能：
1. 配置文件分离 - 支持 kline_config.yaml 配置文件
2. 数据更新模式 - 支持全量更新、增量更新和自动模式
3. 交互式运行模式 - 用户友好的命令行交互界面
4. 向后兼容 - 保持所有原有命令行参数支持

注意：使用前请确保：
1. 已安装QMT客户端
2. QMT客户端已登录并连接
3. Python环境中已安装xtquant模块
"""

import argparse
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

import pandas as pd
from sqlalchemy import Column, String, Float, DateTime, Integer
from sqlalchemy.orm import declarative_base

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from zvt_data_recorder import (
    Mixin, TradableEntity, TimeSeriesDataRecorder, FixedCycleDataRecorder,
    get_db_session, IntervalLevel
)
from zvt_data_recorder.database import register_schema
from zvt_data_recorder.config import init_config, init_logging
from zvt_data_recorder.utils import batch_record_data, run_data_recorder

# 导入新的模块
from kline_config_manager import get_config_manager, init_config_manager
from update_mode_manager import UpdateModeManager
from interactive_mode import InteractiveMode
from zvt_data_recorder.types import Exchange, TradableType

# 创建数据库基类
Base = declarative_base()

# ==================== 数据模型定义 ====================

class Stock(Base, TradableEntity):
    """股票实体模型"""
    __tablename__ = 'stock'
    
    exchange = Column(String(10))      # 交易所
    sector = Column(String(100))       # 板块
    industry = Column(String(100))     # 行业
    market_cap = Column(Float)         # 市值
    list_date = Column(DateTime)       # 上市日期
    delist_date = Column(DateTime)     # 退市日期


class StockKlineDay(Base, Mixin):
    """股票日K线数据"""
    __tablename__ = 'stock_kline_day'
    
    # OHLCV数据
    open = Column(Float)               # 开盘价
    high = Column(Float)               # 最高价
    low = Column(Float)                # 最低价
    close = Column(Float)              # 收盘价
    volume = Column(Float)             # 成交量
    turnover = Column(Float)           # 成交额
    
    # 复权价格
    qfq_open = Column(Float)           # 前复权开盘价
    qfq_high = Column(Float)           # 前复权最高价
    qfq_low = Column(Float)            # 前复权最低价
    qfq_close = Column(Float)          # 前复权收盘价
    
    # 技术指标
    change_pct = Column(Float)         # 涨跌幅
    turnover_rate = Column(Float)      # 换手率


class StockKline1h(Base, Mixin):
    """股票1小时K线数据"""
    __tablename__ = 'stock_kline_1h'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    turnover = Column(Float)


class StockKline30m(Base, Mixin):
    """股票30分钟K线数据"""
    __tablename__ = 'stock_kline_30m'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    turnover = Column(Float)


class StockKline15m(Base, Mixin):
    """股票15分钟K线数据"""
    __tablename__ = 'stock_kline_15m'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    turnover = Column(Float)


class StockKline5m(Base, Mixin):
    """股票5分钟K线数据"""
    __tablename__ = 'stock_kline_5m'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    turnover = Column(Float)


class StockKline1m(Base, Mixin):
    """股票1分钟K线数据"""
    __tablename__ = 'stock_kline_1m'
    
    open = Column(Float)
    high = Column(Float)
    low = Column(Float)
    close = Column(Float)
    volume = Column(Float)
    turnover = Column(Float)


# ==================== K线数据记录器 ====================

class BaseKlineRecorder(FixedCycleDataRecorder):
    """K线数据记录器基类 - QMT数据源"""

    entity_provider = "qmt"
    entity_schema = Stock
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 这里可以初始化数据源API客户端
        # 例如：self.api_client = SomeAPIClient()
    
    def record(self, entity, start, end, size, timestamps):
        """
        记录K线数据 - 使用QMT数据源

        从QMT获取真实的K线数据
        """
        records = []

        if not timestamps:
            return records

        try:
            # 初始化QMT API客户端（如果还没有初始化）
            if not hasattr(self, 'qmt_client'):
                self._init_qmt_client()

            # 从entity.code获取股票代码
            stock_code = entity.code

            # 将时间戳转换为QMT需要的格式
            start_date = timestamps[0].strftime('%Y%m%d')
            end_date = timestamps[-1].strftime('%Y%m%d')

            # 根据级别确定QMT的周期参数
            period = self._get_qmt_period()

            self.logger.info(f"从QMT获取 {stock_code} 的 {period} K线数据: {start_date} ~ {end_date}")

            # 调用QMT API获取K线数据
            qmt_data = self._get_qmt_kline_data(stock_code, start_date, end_date, period)

            if not qmt_data:
                self.logger.warning(f"QMT未返回 {stock_code} 的K线数据")
                return records

            # 转换QMT数据格式为标准格式
            for data_item in qmt_data:
                try:
                    # QMT数据格式转换
                    # QMT返回的时间戳可能是毫秒级Unix时间戳或其他格式
                    time_value = data_item.get('time', data_item.get('date'))

                    if isinstance(time_value, (int, float)):
                        # 如果是数字，假设是毫秒级Unix时间戳
                        if time_value > 1e12:  # 毫秒级时间戳
                            timestamp = pd.to_datetime(time_value, unit='ms')
                        else:  # 秒级时间戳
                            timestamp = pd.to_datetime(time_value, unit='s')
                    else:
                        # 如果是字符串或其他格式
                        timestamp = pd.to_datetime(time_value)

                    self.logger.debug(f"时间戳转换: {time_value} -> {timestamp}")

                    # 确保时间戳在请求范围内（放宽检查条件）
                    if timestamps and len(timestamps) > 0:
                        # 检查时间戳是否在合理范围内
                        start_ts = min(timestamps)
                        end_ts = max(timestamps)
                        if timestamp < start_ts - pd.Timedelta(days=1) or timestamp > end_ts + pd.Timedelta(days=1):
                            self.logger.debug(f"时间戳 {timestamp} 超出范围 {start_ts} ~ {end_ts}，跳过")
                            continue

                    record = {
                        'timestamp': timestamp,
                        'open': round(float(data_item.get('open', 0)), 2),
                        'high': round(float(data_item.get('high', 0)), 2),
                        'low': round(float(data_item.get('low', 0)), 2),
                        'close': round(float(data_item.get('close', 0)), 2),
                        'volume': int(data_item.get('volume', 0)),
                        'turnover': round(float(data_item.get('amount', 0)), 2),
                    }

                    # 如果是日K线，添加复权价格和技术指标
                    if hasattr(self.data_schema, 'qfq_close'):
                        # 计算涨跌幅
                        prev_close = float(data_item.get('pre_close', record['close']))
                        change_pct = ((record['close'] - prev_close) / prev_close * 100) if prev_close > 0 else 0

                        record.update({
                            'qfq_open': round(float(data_item.get('qfq_open', record['open'])), 2),
                            'qfq_high': round(float(data_item.get('qfq_high', record['high'])), 2),
                            'qfq_low': round(float(data_item.get('qfq_low', record['low'])), 2),
                            'qfq_close': round(float(data_item.get('qfq_close', record['close'])), 2),
                            'change_pct': round(change_pct, 2),
                            'turnover_rate': round(float(data_item.get('turnover_rate', 0)), 2),
                        })

                    records.append(record)

                except Exception as e:
                    self.logger.error(f"转换QMT数据项失败: {e}, 数据: {data_item}")
                    continue

            self.logger.info(f"成功从QMT获取 {len(records)} 条 {stock_code} K线数据")

        except Exception as e:
            self.logger.error(f"从QMT获取K线数据失败: {e}")
            # 如果QMT获取失败，可以选择返回空数据或抛出异常
            # 这里选择返回空数据，让调用方处理

        return records

    def _init_qmt_client(self):
        """初始化QMT客户端"""
        try:
            # 尝试导入QMT相关模块
            try:
                from xtquant import xtdata

                # 连接QMT服务器
                result = xtdata.connect()
                if result is not None:
                    self.qmt_client = xtdata
                    self.logger.info("QMT客户端初始化成功")
                else:
                    self.logger.error("QMT连接失败")
                    self.qmt_client = None

            except ImportError:
                self.logger.error("未找到QMT模块，请确保已安装QMT并配置Python环境")
                self.qmt_client = None

        except Exception as e:
            self.logger.error(f"QMT客户端初始化失败: {e}")
            self.qmt_client = None

    def _get_qmt_period(self):
        """获取QMT对应的周期参数"""
        period_map = {
            IntervalLevel.LEVEL_1MIN: '1m',
            IntervalLevel.LEVEL_5MIN: '5m',
            IntervalLevel.LEVEL_15MIN: '15m',
            IntervalLevel.LEVEL_30MIN: '30m',
            IntervalLevel.LEVEL_1HOUR: '1h',
            IntervalLevel.LEVEL_1DAY: '1d',
        }
        return period_map.get(self.level, '1d')

    def _get_qmt_kline_data(self, stock_code, start_date, end_date, period):
        """从QMT获取K线数据"""
        if not self.qmt_client:
            self.logger.error("QMT客户端未初始化")
            return []

        try:
            # 构造QMT股票代码格式
            if stock_code.startswith('6'):
                qmt_code = f"{stock_code}.SH"
            else:
                qmt_code = f"{stock_code}.SZ"

            self.logger.info(f"调用QMT API获取 {qmt_code} 数据")
            self.logger.info(f"请求参数: period={period}, start_time={start_date}, end_time={end_date}")

            # 检查时间范围是否合理
            from datetime import datetime, timedelta
            try:
                start_dt = datetime.strptime(start_date, '%Y%m%d')
                end_dt = datetime.strptime(end_date, '%Y%m%d')
                today = datetime.now()

                # 如果结束时间是未来，调整为今天
                if end_dt > today:
                    end_date = today.strftime('%Y%m%d')
                    self.logger.info(f"调整结束时间为今天: {end_date}")

                # 如果时间范围太小，扩展到至少30天
                if (end_dt - start_dt).days < 30:
                    start_date = (end_dt - timedelta(days=30)).strftime('%Y%m%d')
                    self.logger.info(f"扩展时间范围，新开始时间: {start_date}")

            except Exception as e:
                self.logger.warning(f"时间范围检查失败: {e}")

            # 使用正确的QMT API方法获取历史K线数据
            # 注意：使用get_market_data_ex方法，这是QMT推荐的方法
            try:
                # 方法1：使用get_market_data_ex
                data = self.qmt_client.get_market_data_ex(
                    stock_list=[qmt_code],
                    period=period,
                    start_time=start_date,
                    end_time=end_date,
                    count=-1,
                    dividend_type='front',
                    fill_data=True
                )
            except AttributeError:
                # 方法2：如果get_market_data_ex不存在，尝试get_market_data
                try:
                    data = self.qmt_client.get_market_data(
                        stock_list=[qmt_code],
                        period=period,
                        start_time=start_date,
                        end_time=end_date,
                        count=-1,
                        dividend_type='front',
                        fill_data=True
                    )
                except AttributeError:
                    # 方法3：使用get_local_data（本地数据）
                    data = self.qmt_client.get_local_data(
                        stock_code=qmt_code,
                        period=period,
                        start_time=start_date,
                        end_time=end_date,
                        dividend_type='front'
                    )

            self.logger.info(f"QMT API返回数据类型: {type(data)}")
            if data:
                self.logger.info(f"QMT API返回数据键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

            if data and qmt_code in data:
                return self._convert_qmt_data_format(data[qmt_code])
            elif data and isinstance(data, dict) and len(data) > 0:
                # 如果返回的数据结构不同，尝试获取第一个值
                first_key = list(data.keys())[0]
                return self._convert_qmt_data_format(data[first_key])
            else:
                self.logger.warning(f"QMT未返回 {qmt_code} 的数据，返回数据: {data}")
                return []

        except Exception as e:
            self.logger.error(f"调用QMT API失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def _convert_qmt_data_format(self, qmt_data):
        """转换QMT数据格式"""
        try:
            self.logger.info(f"开始转换QMT数据，数据类型: {type(qmt_data)}")

            # 打印原始数据的前几行用于调试
            if hasattr(qmt_data, 'head'):
                self.logger.info(f"QMT数据前5行:\n{qmt_data.head()}")
            elif isinstance(qmt_data, (list, tuple)) and len(qmt_data) > 0:
                self.logger.info(f"QMT数据示例: {qmt_data[:2]}")
            else:
                self.logger.info(f"QMT数据内容: {qmt_data}")

            # QMT返回的数据格式可能是DataFrame或字典列表
            if hasattr(qmt_data, 'to_dict'):
                # 如果是DataFrame
                self.logger.info("数据是DataFrame格式，转换为字典列表")
                records = qmt_data.to_dict('records')
                self.logger.info(f"转换后记录数: {len(records)}")
            elif hasattr(qmt_data, 'index') and hasattr(qmt_data, 'columns'):
                # 如果是pandas DataFrame但没有to_dict方法
                self.logger.info("数据是pandas DataFrame格式")
                records = []
                for idx, row in qmt_data.iterrows():
                    record = row.to_dict()
                    record['time'] = idx  # 使用索引作为时间
                    records.append(record)
                self.logger.info(f"转换后记录数: {len(records)}")
            else:
                # 如果已经是列表格式
                self.logger.info("数据是列表或其他格式")
                records = qmt_data if isinstance(qmt_data, (list, tuple)) else [qmt_data]
                self.logger.info(f"记录数: {len(records)}")

            if not records:
                self.logger.warning("转换后的记录为空")
                return []

            # 打印第一条记录的字段
            if records:
                self.logger.info(f"第一条记录的字段: {list(records[0].keys())}")

            # 标准化字段名
            standardized_records = []
            for i, record in enumerate(records):
                try:
                    standardized_record = {}

                    # 映射QMT字段名到标准字段名
                    field_mapping = {
                        'time': 'time',
                        'date': 'time',
                        'timestamp': 'time',
                        'open': 'open',
                        'high': 'high',
                        'low': 'low',
                        'close': 'close',
                        'volume': 'volume',
                        'vol': 'volume',
                        'amount': 'amount',
                        'turnover': 'amount',
                        'pre_close': 'pre_close',
                        'preclose': 'pre_close',
                        'turnover_rate': 'turnover_rate',
                        'turn': 'turnover_rate'
                    }

                    for qmt_field, std_field in field_mapping.items():
                        if qmt_field in record:
                            standardized_record[std_field] = record[qmt_field]

                    if standardized_record:
                        standardized_records.append(standardized_record)

                    # 只打印前几条记录的详细信息
                    if i < 3:
                        self.logger.info(f"记录 {i+1} 原始: {record}")
                        self.logger.info(f"记录 {i+1} 标准化: {standardized_record}")

                except Exception as e:
                    self.logger.error(f"转换第 {i+1} 条记录失败: {e}")
                    continue

            self.logger.info(f"最终标准化记录数: {len(standardized_records)}")
            return standardized_records

        except Exception as e:
            self.logger.error(f"转换QMT数据格式失败: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return []


class StockKlineDayRecorder(BaseKlineRecorder):
    """股票日K线记录器 - QMT数据源"""
    provider = "qmt"
    data_schema = StockKlineDay
    level = IntervalLevel.LEVEL_1DAY


class StockKline1hRecorder(BaseKlineRecorder):
    """股票1小时K线记录器 - QMT数据源"""
    provider = "qmt"
    data_schema = StockKline1h
    level = IntervalLevel.LEVEL_1HOUR


class StockKline30mRecorder(BaseKlineRecorder):
    """股票30分钟K线记录器 - QMT数据源"""
    provider = "qmt"
    data_schema = StockKline30m
    level = IntervalLevel.LEVEL_30MIN


class StockKline15mRecorder(BaseKlineRecorder):
    """股票15分钟K线记录器 - QMT数据源"""
    provider = "qmt"
    data_schema = StockKline15m
    level = IntervalLevel.LEVEL_15MIN


class StockKline5mRecorder(BaseKlineRecorder):
    """股票5分钟K线记录器 - QMT数据源"""
    provider = "qmt"
    data_schema = StockKline5m
    level = IntervalLevel.LEVEL_5MIN


class StockKline1mRecorder(BaseKlineRecorder):
    """股票1分钟K线记录器 - QMT数据源"""
    provider = "qmt"
    data_schema = StockKline1m
    level = IntervalLevel.LEVEL_1MIN


# ==================== 配置和初始化 ====================

def setup_database():
    """设置数据库和注册模式 - 使用QMT数据源"""
    print("📊 设置数据库和注册模式（QMT数据源）...")

    # 注册数据模式 - 统一使用QMT作为数据提供者
    providers = ["qmt"]

    register_schema(
        providers=providers,
        db_name="stock_kline_qmt",
        schema_base=Base,
        entity_type="stock"
    )

    # 注册提供者到各个模式
    for schema_cls in [Stock, StockKlineDay, StockKline1h, StockKline30m,
                       StockKline15m, StockKline5m, StockKline1m]:
        for provider in providers:
            schema_cls.register_provider(provider)

    # 注册记录器 - 统一使用QMT
    StockKlineDay.register_recorder_cls("qmt", StockKlineDayRecorder)
    StockKline1h.register_recorder_cls("qmt", StockKline1hRecorder)
    StockKline30m.register_recorder_cls("qmt", StockKline30mRecorder)
    StockKline15m.register_recorder_cls("qmt", StockKline15mRecorder)
    StockKline5m.register_recorder_cls("qmt", StockKline5mRecorder)
    StockKline1m.register_recorder_cls("qmt", StockKline1mRecorder)
    
    # 创建数据库表
    # 为股票实体创建表
    stock_session = get_db_session(provider="qmt", data_schema=Stock)
    Base.metadata.create_all(stock_session.bind)
    stock_session.close()

    # 为各个时间周期的K线数据创建表 - 统一使用QMT
    kline_schemas = [
        ("qmt", StockKlineDay),
        ("qmt", StockKline1h),
        ("qmt", StockKline30m),
        ("qmt", StockKline15m),
        ("qmt", StockKline5m),
        ("qmt", StockKline1m),
    ]

    for provider, schema in kline_schemas:
        try:
            session = get_db_session(provider=provider, data_schema=schema)
            Base.metadata.create_all(session.bind)
            session.close()
        except Exception as e:
            print(f"⚠️ 创建 {provider} {schema.__name__} 表失败: {e}")
    
    print("✅ 数据库设置完成")


def get_qmt_stock_list():
    """从QMT获取真实股票列表"""
    try:
        import xtquant.xtdata as xt

        # 连接QMT
        result = xt.connect()
        if result is None:
            return None

        # 获取股票列表
        stocks = xt.get_stock_list_in_sector('沪深A股')
        if stocks and len(stocks) > 0:
            print(f"📡 从QMT获取到 {len(stocks)} 只股票")
            return stocks[:50]  # 限制为前50只股票，避免太多
        else:
            return None

    except Exception as e:
        print(f"❌ 从QMT获取股票列表失败: {e}")
        return None

def create_sample_stocks():
    """创建股票数据 - QMT数据源"""
    print("🏗️ 创建股票数据（QMT数据源）...")

    session = get_db_session(provider="qmt", data_schema=Stock)

    # 首先尝试从QMT获取真实股票列表
    qmt_stocks = get_qmt_stock_list()

    if qmt_stocks:
        print(f"✅ 使用QMT真实股票列表，共 {len(qmt_stocks)} 只股票")
        created_stocks = []

        for i, stock_code in enumerate(qmt_stocks):
            try:
                # 解析股票代码和交易所
                if '.' in stock_code:
                    code, exchange = stock_code.split('.')
                    exchange = exchange.upper()
                else:
                    code = stock_code
                    exchange = 'SH' if code.startswith('6') else 'SZ'

                stock_id = f"stock_{exchange.lower()}_{code}"

                existing = session.query(Stock).filter(Stock.id == stock_id).first()
                if not existing:
                    stock = Stock(
                        id=stock_id,
                        entity_id=stock_id,
                        code=code,
                        name=f"股票{code}",  # 简化名称
                        exchange=exchange,
                        sector='未知',
                        industry='未知',
                        list_date=datetime(2010, 1, 1),
                    )
                    session.add(stock)
                    created_stocks.append(stock_id)
                else:
                    created_stocks.append(stock_id)

                if i % 10 == 0:  # 每10只股票提交一次
                    session.commit()

            except Exception as e:
                print(f"⚠️ 处理股票 {stock_code} 失败: {e}")
                continue

        session.commit()
        session.close()
        print(f"✅ 创建了 {len(created_stocks)} 只股票")
        return created_stocks

    else:
        print("🔄 使用示例股票数据...")
        # 示例股票列表
        sample_stocks = [
            # 沪市主板
            {'code': '600000', 'name': '浦发银行', 'exchange': 'SH', 'sector': '金融', 'industry': '银行'},
            {'code': '600036', 'name': '招商银行', 'exchange': 'SH', 'sector': '金融', 'industry': '银行'},
            {'code': '600519', 'name': '贵州茅台', 'exchange': 'SH', 'sector': '消费', 'industry': '白酒'},
            {'code': '600887', 'name': '伊利股份', 'exchange': 'SH', 'sector': '消费', 'industry': '乳制品'},

            # 深市主板
            {'code': '000001', 'name': '平安银行', 'exchange': 'SZ', 'sector': '金融', 'industry': '银行'},
            {'code': '000002', 'name': '万科A', 'exchange': 'SZ', 'sector': '地产', 'industry': '房地产开发'},
            {'code': '000858', 'name': '五粮液', 'exchange': 'SZ', 'sector': '消费', 'industry': '白酒'},

            # 创业板
            {'code': '300015', 'name': '爱尔眼科', 'exchange': 'SZ', 'sector': '医疗', 'industry': '医疗服务'},
            {'code': '300059', 'name': '东方财富', 'exchange': 'SZ', 'sector': '金融', 'industry': '证券'},
            {'code': '300750', 'name': '宁德时代', 'exchange': 'SZ', 'sector': '新能源', 'industry': '电池'},
        ]
    
    # 示例股票列表（实际使用时应该从股票列表API获取）
    sample_stocks = [
        # 沪市主板
        {'code': '600000', 'name': '浦发银行', 'exchange': 'SH', 'sector': '金融', 'industry': '银行'},
        {'code': '600036', 'name': '招商银行', 'exchange': 'SH', 'sector': '金融', 'industry': '银行'},
        {'code': '600519', 'name': '贵州茅台', 'exchange': 'SH', 'sector': '消费', 'industry': '白酒'},
        {'code': '600887', 'name': '伊利股份', 'exchange': 'SH', 'sector': '消费', 'industry': '乳制品'},
        
        # 深市主板
        {'code': '000001', 'name': '平安银行', 'exchange': 'SZ', 'sector': '金融', 'industry': '银行'},
        {'code': '000002', 'name': '万科A', 'exchange': 'SZ', 'sector': '地产', 'industry': '房地产开发'},
        {'code': '000858', 'name': '五粮液', 'exchange': 'SZ', 'sector': '消费', 'industry': '白酒'},
        
        # 创业板
        {'code': '300015', 'name': '爱尔眼科', 'exchange': 'SZ', 'sector': '医疗', 'industry': '医疗服务'},
        {'code': '300059', 'name': '东方财富', 'exchange': 'SZ', 'sector': '金融', 'industry': '证券'},
        {'code': '300750', 'name': '宁德时代', 'exchange': 'SZ', 'sector': '新能源', 'industry': '电池'},
    ]
    
    for stock_info in sample_stocks:
        stock_id = f"stock_{stock_info['exchange'].lower()}_{stock_info['code']}"
        
        existing = session.query(Stock).filter(Stock.id == stock_id).first()
        if not existing:
            stock = Stock(
                id=stock_id,
                entity_id=stock_id,
                code=stock_info['code'],
                name=stock_info['name'],
                exchange=stock_info['exchange'],
                sector=stock_info['sector'],
                industry=stock_info['industry'],
                list_date=datetime(2010, 1, 1),  # 示例上市日期
            )
            session.add(stock)
    
    session.commit()
    session.close()
    
    print(f"✅ 创建了 {len(sample_stocks)} 只示例股票")
    return [f"stock_{s['exchange'].lower()}_{s['code']}" for s in sample_stocks]


# ==================== 主要功能函数 ====================

def record_kline_data(levels: List[str], entity_ids: List[str] = None,
                     start_date: str = None, end_date: str = None,
                     force_update: bool = False, sleeping_time: int = 1,
                     update_mode: str = 'auto', config_manager=None):
    """
    记录K线数据 (支持多种更新模式)

    :param levels: 时间周期列表，如 ['1d', '1h', '30m']
    :param entity_ids: 股票ID列表，如果为None则记录所有股票
    :param start_date: 开始日期，格式：YYYY-MM-DD
    :param end_date: 结束日期，格式：YYYY-MM-DD
    :param force_update: 是否强制更新
    :param sleeping_time: 休眠时间（秒）
    :param update_mode: 更新模式 ('full', 'incremental', 'auto')
    :param config_manager: 配置管理器实例
    """
    print(f"📈 开始记录K线数据")
    print(f"更新模式: {update_mode}")
    print(f"时间周期: {levels}")
    print(f"股票数量: {len(entity_ids) if entity_ids else '全部'}")
    print(f"时间范围: {start_date} ~ {end_date}")
    print("=" * 50)

    # 如果有配置管理器，使用更新模式管理器处理时间范围
    if config_manager:
        update_manager = UpdateModeManager(config_manager)

        # 验证更新参数
        if not update_manager.validate_update_parameters(update_mode, levels, start_date, end_date):
            print("❌ 更新参数验证失败")
            return

        # 显示更新计划
        update_manager.print_update_plan(update_mode, levels, entity_ids, start_date, end_date)

    # 时间周期到记录器的映射 - 统一使用QMT
    provider = config_manager.get_data_provider() if config_manager else "qmt"
    level_recorder_map = {
        '1d': (StockKlineDay, provider),
        '1h': (StockKline1h, provider),
        '30m': (StockKline30m, provider),
        '15m': (StockKline15m, provider),
        '5m': (StockKline5m, provider),
        '1m': (StockKline1m, provider),
    }

    # 如果没有指定股票，获取所有股票
    if not entity_ids:
        try:
            stocks_df = Stock.query_data(provider=provider, return_type="df")
            if stocks_df.empty:
                print("❌ 数据库中没有股票数据，请先创建股票数据")
                return

            # 检查DataFrame中的列名
            print(f"📊 股票数据表列名: {list(stocks_df.columns)}")

            # 尝试不同的字段名
            if 'entity_id' in stocks_df.columns:
                entity_ids = stocks_df['entity_id'].tolist()
            elif 'id' in stocks_df.columns:
                entity_ids = stocks_df['id'].tolist()
            else:
                print(f"❌ 股票数据表中没有找到entity_id或id字段，可用字段: {list(stocks_df.columns)}")
                return

            print(f"📊 获取到 {len(entity_ids)} 只股票")
        except Exception as e:
            print(f"❌ 获取股票数据失败: {e}")
            # 如果查询失败，使用创建的示例股票
            print("🔄 使用示例股票数据...")
            entity_ids = create_sample_stocks()
            print(f"📊 使用 {len(entity_ids)} 只示例股票")

    total_success = 0
    total_failed = 0

    # 按时间周期记录数据
    for level in levels:
        if level not in level_recorder_map:
            print(f"❌ 不支持的时间周期: {level}")
            continue

        data_schema, level_provider = level_recorder_map[level]

        print(f"\n🔄 开始记录 {level} K线数据...")

        # 为每个时间周期确定实际的时间范围
        actual_start_date = start_date
        actual_end_date = end_date

        if config_manager:
            update_manager = UpdateModeManager(config_manager)
            actual_mode = update_manager.determine_update_mode(level, entity_ids)
            actual_start_date, actual_end_date = update_manager.get_update_time_range(
                actual_mode, level, entity_ids, start_date, end_date
            )
            print(f"📅 {level} 实际时间范围: {actual_start_date} ~ {actual_end_date}")

        # 转换日期
        start_timestamp = pd.to_datetime(actual_start_date) if actual_start_date else None
        end_timestamp = pd.to_datetime(actual_end_date) if actual_end_date else None

        try:
            # 使用工具函数批量记录
            unfinished = run_data_recorder(
                domain=data_schema,
                data_provider=level_provider,
                entity_ids=entity_ids,
                start_timestamp=start_timestamp,
                end_timestamp=end_timestamp,
                force_update=force_update,
                sleeping_time=sleeping_time,
                return_unfinished=True
            )

            success_count = len(entity_ids) - len(unfinished)
            failed_count = len(unfinished)

            print(f"✅ {level} K线记录完成: 成功 {success_count}, 失败 {failed_count}")

            total_success += success_count
            total_failed += failed_count

            if unfinished:
                print(f"⚠️ 未完成的股票: {unfinished[:5]}{'...' if len(unfinished) > 5 else ''}")

        except Exception as e:
            print(f"❌ {level} K线记录失败: {e}")
            total_failed += len(entity_ids)

    print(f"\n🎉 全部K线数据记录完成!")
    print(f"总计: 成功 {total_success}, 失败 {total_failed}")


def get_schema_by_level(level: str):
    """
    根据时间周期获取对应的数据模式类

    :param level: 时间周期
    :return: 数据模式类
    """
    level_schema_map = {
        '1d': StockKlineDay,
        '1h': StockKline1h,
        '30m': StockKline30m,
        '15m': StockKline15m,
        '5m': StockKline5m,
        '1m': StockKline1m,
    }
    return level_schema_map.get(level)


def show_data_summary():
    """显示数据统计摘要"""
    print("\n📊 数据统计摘要")
    print("=" * 50)

    # 统计各个时间周期的数据量 - QMT数据源
    schemas = [
        (StockKlineDay, "日K线", "qmt"),
        (StockKline1h, "1小时K线", "qmt"),
        (StockKline30m, "30分钟K线", "qmt"),
        (StockKline15m, "15分钟K线", "qmt"),
        (StockKline5m, "5分钟K线", "qmt"),
        (StockKline1m, "1分钟K线", "qmt"),
    ]

    for schema, name, provider in schemas:
        try:
            # 直接获取总数据量
            from zvt_data_recorder.database.session import get_count
            count = get_count(schema, provider=provider)

            if count > 0:
                # 获取时间范围
                try:
                    from sqlalchemy import text
                    latest_df = schema.query_data(
                        provider=provider,
                        return_type="df",
                        order=text("timestamp desc"),
                        limit=1
                    )
                    earliest_df = schema.query_data(
                        provider=provider,
                        return_type="df",
                        order=text("timestamp asc"),
                        limit=1
                    )

                    # 重置索引以确保timestamp列可访问
                    if not latest_df.empty and latest_df.index.names and 'timestamp' in latest_df.index.names:
                        latest_df = latest_df.reset_index()
                    if not earliest_df.empty and earliest_df.index.names and 'timestamp' in earliest_df.index.names:
                        earliest_df = earliest_df.reset_index()

                    if not latest_df.empty and not earliest_df.empty and 'timestamp' in latest_df.columns and 'timestamp' in earliest_df.columns:
                        latest_time = latest_df.iloc[0]['timestamp']
                        earliest_time = earliest_df.iloc[0]['timestamp']
                        print(f"{name}: {count:,} 条记录 ({earliest_time} ~ {latest_time})")
                    else:
                        print(f"{name}: {count:,} 条记录")

                except Exception as e:
                    print(f"{name}: {count:,} 条记录 (时间范围查询失败: {e})")
            else:
                print(f"{name}: 0 条记录")
        except Exception as e:
            print(f"{name}: 查询失败 - {e}")


def query_kline_data(code: str, level: str = '1d', limit: int = 10):
    """
    查询指定股票的K线数据

    :param code: 股票代码
    :param level: 时间周期
    :param limit: 限制条数
    """
    print(f"\n🔍 查询股票 {code} 的 {level} K线数据 (最近{limit}条)")
    print("=" * 50)

    # 构造entity_id
    if code.startswith('6'):
        entity_id = f"stock_sh_{code}"
    else:
        entity_id = f"stock_sz_{code}"

    # 选择对应的数据模式
    level_schema_map = {
        '1d': StockKlineDay,
        '1h': StockKline1h,
        '30m': StockKline30m,
        '15m': StockKline15m,
        '5m': StockKline5m,
        '1m': StockKline1m,
    }

    if level not in level_schema_map:
        print(f"❌ 不支持的时间周期: {level}")
        return

    schema = level_schema_map[level]

    try:
        from sqlalchemy import text
        df = schema.query_data(
            provider="qmt",
            entity_ids=[entity_id],
            return_type="df",
            order=text("timestamp desc"),
            limit=limit
        )

        if df.empty:
            print(f"❌ 没有找到股票 {code} 的 {level} K线数据")
            return

        # 重置索引以确保timestamp列可访问
        if df.index.names and 'timestamp' in df.index.names:
            df = df.reset_index()

        # 显示数据
        display_columns = []
        for col in ['timestamp', 'entity_id', 'open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                display_columns.append(col)

        if 'turnover' in df.columns:
            display_columns.append('turnover')
        if 'change_pct' in df.columns:
            display_columns.append('change_pct')

        print(df[display_columns].to_string(index=False))
        print(f"\n✅ 共查询到 {len(df)} 条记录")

    except Exception as e:
        print(f"❌ 查询失败: {e}")


def main():
    """主函数 (优化版)"""
    parser = argparse.ArgumentParser(
        description="ZVT K线数据记录器 - QMT数据源版本 (优化版)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例（QMT数据源）:
  # 默认启动交互式模式（推荐）
  python start_kline_recorder.py

  # 强制启动交互式模式
  python start_kline_recorder.py --interactive

  # 禁用交互式模式，直接显示帮助
  python start_kline_recorder.py --no-interactive

  # 记录所有股票的日K线数据
  python start_kline_recorder.py --levels 1d

  # 使用增量更新模式
  python start_kline_recorder.py --levels 1d --update-mode incremental

  # 记录多个时间周期的K线数据
  python start_kline_recorder.py --levels 1d 1h 30m

  # 记录指定股票的K线数据
  python start_kline_recorder.py --levels 1d --codes 600000 000001

  # 记录指定时间范围的数据
  python start_kline_recorder.py --levels 1d --start-date 2023-01-01 --end-date 2023-12-31

  # 查看数据统计
  python start_kline_recorder.py --summary

  # 查询指定股票的K线数据
  python start_kline_recorder.py --query 600000 --level 1d --limit 20

  # 使用自定义配置文件
  python start_kline_recorder.py --config my_config.yaml --levels 1d

注意：使用前请确保QMT客户端已启动并登录
        """
    )

    parser.add_argument(
        '--levels',
        nargs='+',
        choices=['1d', '1h', '30m', '15m', '5m', '1m'],
        help='要记录的K线时间周期'
    )

    parser.add_argument(
        '--codes',
        nargs='+',
        help='要记录的股票代码列表'
    )

    parser.add_argument(
        '--start-date',
        help='开始日期 (YYYY-MM-DD)'
    )

    parser.add_argument(
        '--end-date',
        help='结束日期 (YYYY-MM-DD)'
    )

    parser.add_argument(
        '--force-update',
        action='store_true',
        help='强制更新已有数据'
    )

    parser.add_argument(
        '--sleeping-time',
        type=int,
        default=1,
        help='记录间隔休眠时间（秒），默认1秒'
    )

    parser.add_argument(
        '--summary',
        action='store_true',
        help='显示数据统计摘要'
    )

    parser.add_argument(
        '--query',
        help='查询指定股票代码的K线数据'
    )

    parser.add_argument(
        '--level',
        default='1d',
        choices=['1d', '1h', '30m', '15m', '5m', '1m'],
        help='查询时使用的时间周期，默认1d'
    )

    parser.add_argument(
        '--limit',
        type=int,
        default=10,
        help='查询时限制返回的记录数，默认10'
    )

    parser.add_argument(
        '--data-path',
        default='D:\\MyData\\zvt-data',
        help='数据存储路径，默认 D:\\MyData\\zvt-data'
    )

    parser.add_argument(
        '--verbose',
        action='store_true',
        help='显示详细日志'
    )

    # 新增参数
    parser.add_argument(
        '--config',
        help='配置文件路径，默认为 kline_config.yaml'
    )

    parser.add_argument(
        '--update-mode',
        choices=['full', 'incremental', 'auto'],
        default='auto',
        help='数据更新模式：full(全量), incremental(增量), auto(自动)，默认auto'
    )

    parser.add_argument(
        '--interactive',
        action='store_true',
        help='强制启动交互式模式'
    )

    parser.add_argument(
        '--no-interactive',
        action='store_true',
        help='禁用交互式模式，直接显示帮助信息'
    )

    parser.add_argument(
        '--show-config',
        action='store_true',
        help='显示当前配置并退出'
    )

    args = parser.parse_args()

    # 初始化配置管理器
    config_manager = init_config_manager(args.config)

    # 从命令行参数更新配置
    config_manager.update_from_args(args)

    # 显示配置并退出
    if args.show_config:
        config_manager.print_config_summary()
        return

    # 初始化配置
    config = {
        'DATA_PATH': config_manager.get_data_path(),
        'LOG_LEVEL': 'DEBUG' if config_manager.is_verbose() else 'INFO',
        'EMAIL_ENABLED': False,
    }
    init_config(config)
    init_logging()

    print("🚀 ZVT K线数据记录器 - QMT数据源版本 (优化版)")
    print("=" * 60)
    print(f"数据存储路径: {config_manager.get_data_path()}")
    print(f"数据源: {config_manager.get_data_provider().upper()}")
    print("⚠️  请确保QMT客户端已启动并登录")

    try:
        # 设置数据库
        setup_database()

        # 创建示例股票数据
        entity_ids = create_sample_stocks()

        # 初始化更新模式管理器
        update_manager = UpdateModeManager(config_manager)

        # 检查是否有具体的操作参数
        has_action_params = any([
            args.summary,
            args.query,
            args.levels
        ])

        # 交互式模式判断逻辑：
        # 1. 如果明确指定 --no-interactive，则禁用交互式模式
        # 2. 如果明确指定 --interactive，则强制启用交互式模式
        # 3. 如果配置文件启用交互式模式且没有具体操作参数，则启用交互式模式
        should_use_interactive = (
            not args.no_interactive and  # 没有禁用交互式模式
            (args.interactive or  # 明确指定启用
             (config_manager.is_interactive_enabled() and not has_action_params))  # 配置启用且无具体操作
        )

        if should_use_interactive:
            interactive = InteractiveMode(config_manager, update_manager)
            config = interactive.run_interactive_mode()

            if config:
                # 使用交互式配置执行记录
                record_kline_data(
                    levels=config['levels'],
                    entity_ids=config['entity_ids'],
                    start_date=config['start_date'],
                    end_date=config['end_date'],
                    force_update=config['force_update'],
                    sleeping_time=config['sleeping_time'],
                    update_mode=config['update_mode'],
                    config_manager=config_manager
                )
                # 记录完成后显示统计
                show_data_summary()
            return

        # 处理股票代码参数
        if args.codes:
            # 将股票代码转换为entity_id
            selected_entity_ids = []
            for code in args.codes:
                if code.startswith('6'):
                    entity_id = f"stock_sh_{code}"
                else:
                    entity_id = f"stock_sz_{code}"

                if entity_id in entity_ids:
                    selected_entity_ids.append(entity_id)
                else:
                    print(f"⚠️ 股票代码 {code} 不在股票列表中")

            entity_ids = selected_entity_ids

        # 执行相应的操作
        if args.summary:
            show_data_summary()
        elif args.query:
            query_kline_data(args.query, args.level, args.limit)
        elif args.levels:
            record_kline_data(
                levels=args.levels,
                entity_ids=entity_ids,
                start_date=args.start_date,
                end_date=args.end_date,
                force_update=args.force_update,
                sleeping_time=args.sleeping_time,
                update_mode=args.update_mode,
                config_manager=config_manager
            )
            # 记录完成后显示统计
            show_data_summary()
        else:
            # 没有指定具体操作参数时的处理
            if args.no_interactive:
                # 明确禁用交互式模式，显示帮助信息
                print("🎯 ZVT K线数据记录器")
                print("=" * 50)
                print("您已禁用交互式模式，请指定具体的操作参数。")
                print("\n常用命令示例:")
                print("  python start_kline_recorder.py --levels 1d")
                print("  python start_kline_recorder.py --summary")
                print("  python start_kline_recorder.py --help")
                print("\n或者使用交互式模式:")
                print("  python start_kline_recorder.py --interactive")
            else:
                # 默认启动交互式模式
                print("🎯 欢迎使用ZVT K线数据记录器！")
                print("未指定参数，自动启动交互式模式...")
                print("=" * 50)

                interactive = InteractiveMode(config_manager, update_manager)
                config = interactive.run_interactive_mode()

                if config:
                    # 使用交互式配置执行记录
                    record_kline_data(
                        levels=config['levels'],
                        entity_ids=config['entity_ids'],
                        start_date=config['start_date'],
                        end_date=config['end_date'],
                        force_update=config['force_update'],
                        sleeping_time=config['sleeping_time'],
                        update_mode=config['update_mode'],
                        config_manager=config_manager
                    )
                    # 记录完成后显示统计
                    show_data_summary()

    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
