#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查数据库表结构
"""

import sqlite3
import os
import sys

def check_database_tables(db_path):
    """检查数据库中的表"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    print(f"🔍 检查数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if tables:
            print(f"✅ 找到 {len(tables)} 个表:")
            for table in tables:
                table_name = table[0]
                print(f"  - {table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name});")
                columns = cursor.fetchall()
                print(f"    字段数: {len(columns)}")
                
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"    记录数: {count}")
                print()
        else:
            print("❌ 没有找到任何表")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")

def main():
    """主函数"""
    print("🔍 数据库表结构检查工具")
    print("=" * 50)
    
    # 检查各个数据库文件
    db_files = [
        "kline_data/qmt/qmt_stock_kline_qmt.db",
        "kline_data/stock/stock_stock_kline_qmt.db",
    ]
    
    for db_file in db_files:
        check_database_tables(db_file)
        print("-" * 30)

if __name__ == '__main__':
    main()
